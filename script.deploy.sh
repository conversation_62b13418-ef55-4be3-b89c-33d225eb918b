#!/bin/bash
set -e

PROCESS_NAME="app-api"

echo "Deploying $PROCESS_NAME..."
echo "Current version of $PROCESS_NAME: $(sudo -u ubuntu pm2 show $PROCESS_NAME | grep version)"
echo "Current project version: $(npm pkg get version | tr -d '\"')"

echo "🔄 Fetching Git..."
git fetch

echo "⬇️ Pulling Git..."
git pull

echo "🔂 Installing NPM Dependencies..."
npm install

echo "🚮 Deleting Dist Build..."
rm -rf dist

echo "🛠️ Production Build Started..."
npm run build
echo "🟢 Build Success..."

echo "🔧 Updating project version..."
# npm version patch --no-git-tag-version     # without tag
npm version patch
git push --follow-tags
echo "Deployed project version: $(npm pkg get version | tr -d '\"')"

echo "🔃 Restarting Process Manager"
EXISTS=$(pm2 jlist | jq -r ".[] | select(.name==\"$PROCESS_NAME\") | .name")

if [ "$EXISTS" != "$PROCESS_NAME" ]; then
    echo "🚫 Process '$PROCESS_NAME' not found. Adding and starting..."
    sudo -u ubuntu pm2 start "$SCRIPT_PATH" --name "$PROCESS_NAME"
    sudo -u ubuntu pm2 save
else
    echo "✅ Process '$PROCESS_NAME' found. Restarting..."
    sudo -u ubuntu pm2 restart $PROCESS_NAME
fi
echo "✅ Process started ${PROCESS_NAME}"


# echo "🔄 Reloading systemd daemon configuration"
# sudo systemctl daemon-reload
# echo "🚀 Restarting Nginx"
# sudo systemctl restart nginx
# echo "✅ Web Nginx Server Restarted"
