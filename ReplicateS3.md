Here's a **small and clear guide** to replicate S3 buckets across AWS regions (e.g., from `ap-south-1` to `us-west-1`) using the **web console**, with appropriate naming conventions and using the IAM role you mentioned earlier (`s3_replication` with `AmazonS3FullAccess` policy).

---

## ✅ Goal:

Replicate selected S3 buckets (like `final-recording`) from **India (`ap-south-1`)** to **US (`us-west-1`)** for **read optimization**.

---

## 📦 Step 1: Naming Convention for Buckets

Follow a consistent naming convention to manage region-specific replicas:

| Purpose               | Region     | Bucket Name             |
| --------------------- | ---------- | ----------------------- |
| Original write bucket | ap-south-1 | `final-recording` |
| Replica read bucket   | us-west-1  | `final-recording-us`    |

*You can replace "final-recording" with other services as needed.*

## 🔄 Bidirectional Sync Note
Only the following buckets have bidirectional S3 sync setup between regions:
- `final-recording` (ap-south-1) ↔ `user-final-recording-us` (us-west-1)

## 📋 Complete List of Buckets with India to US Replication

| Bucket Name | Region |
| ----------- | ------ |
| annotated-songs-us | us-west-1 |
| annotated-songs-wav-cache-us | us-west-1 |
| annotated-songs-wav-us | us-west-1 |
| lyrics-timeline-us | us-west-1 |
| melo-common-images-us | us-west-1 |
| melodyze-24hrs-ttl-us | us-west-1 |
| melodyze-user-feeds-us | us-west-1 |
| song-thumbnail-us | us-west-1 |
| user-final-recording-us | us-west-1 |

---

## 🔐 Step 2: IAM Role Setup

You already have an IAM Role: `s3_replication`

✅ Attached Policy:

* `AmazonS3FullAccess`

🟨 Check Trust Relationship:

* Go to **IAM > Roles > s3\_replication > Trust relationships**
* It should look like this:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "s3.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
```

If not, **edit the trust relationship** and replace with the above.

---

## 🔄 Step 3: Enable Versioning

On **both source and destination buckets**, versioning must be enabled:

1. Go to **S3 > final-recording**
2. **Properties > Versioning > Enable**
3. Repeat for **final-recording-us**

---

## 🔁 Step 4: Create Replication Rule

Go to:
**S3 > final-recording > Management > Replication rules > Create rule**

### 🧩 Rule Configuration:

* **Name**: `replicate-to-us`
* **Status**: Enabled

### 📁 Source:

* Choose “This rule applies to all objects in the bucket”

### 🎯 Destination:

* **Bucket**: Select `final-recording-us`
* **Destination Region**: `us-west-1`

### 🔐 Permissions:

* **IAM Role**: Select `s3_replication`

### ✅ Options:

* Enable "Replicate existing objects" if you want to copy historical data
* Enable "Replicate delete markers" only if needed

### Save.

---

## 🧪 Step 5: Testing

Upload a file to `final-recording`
Wait \~1 minute — it should auto-appear in `final-recording-us`

---

## ❓Notes

* Replication is **asynchronous** – usually happens within minutes
* Only **source-side writes** get replicated
* Destination is **read-only** for replication (not bi-directional)

---

Let me know if you want to **add filters (e.g., prefix, tags)** or **replicate only specific buckets**, or want help writing a verification script.
