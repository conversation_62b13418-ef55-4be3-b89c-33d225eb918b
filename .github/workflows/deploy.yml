name: Deploy to AWS Server

on:
  pull_request:
    branches:
      - main-dev
    types:
      - closed
  workflow_dispatch:
jobs:
  deploy:
    if: github.event_name == 'workflow_dispatch' || github.event.pull_request.merged == true  # Runs on manual trigger OR merged PR
    runs-on: ubuntu-latest

    steps:
      - name: Setup SSH Key
        run: |
          echo "${{ secrets.AWS_SSH_KEY }}" > melodyze-testing-apps.pem
          chmod 600 melodyze-testing-apps.pem

      - name: Deploy to Server
        run: |
          ssh -o StrictHostKeyChecking=no -i melodyze-testing-apps.pem <EMAIL> << 'EOF'
            cd backend-v1
            sudo ./script.deploy.sh
          EOF

      - name: Cleanup SSH Key
        run: rm -f melodyze-testing-apps.pem
