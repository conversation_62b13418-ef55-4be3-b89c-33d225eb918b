#!/bin/bash
# This script sets up a single custom domain with Nginx and obtains a Let's Encrypt SSL certificate.

# Ensure the script is run as root.
if [ "$(id -u)" -ne 0 ]; then
  echo "Please run this script as root (e.g., using sudo)."
  exit 1
fi

# echo "Installing required packages..."
# npm i
# echo "Building the project..."
# rm -rf dist
# npm run build
# echo "Starting the Node.js server..."
# pm2 start dist/index.js --name app-api

DOMAIN="app-api.melodyze.ai"
UPSTREAM_PORT=8002
LE_EMAIL="<EMAIL>"

NGINX_AVAILABLE="/etc/nginx/sites-available"
NGINX_ENABLED="/etc/nginx/sites-enabled"
CONF_FILE="${NGINX_AVAILABLE}/${DOMAIN}"

# Create an Nginx configuration for the domain
echo "Creating Nginx configuration for ${DOMAIN}..."
cat > "$CONF_FILE" <<EOF
server {
    listen 80;
    server_name ${DOMAIN};

    location / {
        proxy_pass http://localhost:${UPSTREAM_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable the configuration
ln -sf "$CONF_FILE" "${NGINX_ENABLED}/${DOMAIN}"

# Test Nginx configuration and reload Nginx if the test passes
if nginx -t; then
  systemctl reload nginx
  echo "Nginx configuration is valid and has been reloaded."
else
  echo "ERROR: Nginx configuration test failed."
  exit 1
fi

# Obtain and install SSL certificate using Certbot
echo "Obtaining SSL certificate for ${DOMAIN}..."
certbot --nginx -d "$DOMAIN" --non-interactive --agree-tos --email "$LE_EMAIL"
echo "Setup complete! Domain ${DOMAIN} is now configured with SSL."