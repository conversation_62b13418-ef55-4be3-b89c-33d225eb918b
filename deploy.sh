#!/bin/bash
set -e

# Script documentation
# Usage: ./deploy.sh -r|--region <region> [-b|--branch <branch>]
# Options:
#   -r, --region   Specify AWS region (ap-south-1 or us-west-1)
#   -b, --branch   Specify git branch to deploy (default: current branch)

PROCESS_NAME="app-api"
DEFAULT_BRANCH=$(git branch --show-current)

echo "Deploying $PROCESS_NAME..."

# Clean up any local changes before starting
echo "🧹 Cleaning up local changes..."
if [ -n "$(git status --porcelain)" ]; then
    echo "Found uncommitted changes. Discarding all local changes..."
    git reset --hard HEAD
    git clean -fd
    echo "✅ Local changes have been discarded"
else
    echo "No local changes to clean"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
    -r | --region)
        DEPLOY_REGION="$2"
        shift # past argument
        shift # past value
        ;;
    -b | --branch)
        DEPLOY_BRANCH="$2"
        shift # past argument
        shift # past value
        ;;
    *)
        echo "Error: Unknown option $1"
        exit 1
        ;;
    esac
done

# Validate region
if [ -z "$DEPLOY_REGION" ]; then
    echo "Error: No region provided. Usage: ./deploy.sh -r|--region <region> [-b|--branch <branch>]"
    exit 1
elif [ "$DEPLOY_REGION" != "ap-south-1" ] && [ "$DEPLOY_REGION" != "us-west-1" ]; then
    echo "Error: Invalid region. Must be either ap-south-1 or us-west-1"
    exit 1
fi

# Set default branch if not specified
if [ -z "$DEPLOY_BRANCH" ]; then
    DEPLOY_BRANCH="$DEFAULT_BRANCH"
fi

echo "Current version of $PROCESS_NAME: $(sudo -u ubuntu pm2 show $PROCESS_NAME | grep version)"
echo "Current project version: $(npm pkg get version | tr -d '\"')"

echo "🔄 Fetching Git..."
git fetch

echo "⬇️ Checking out branch $DEPLOY_BRANCH..."
git checkout "$DEPLOY_BRANCH"

echo "⬇️ Pulling Git..."
git pull origin "$DEPLOY_BRANCH"

echo "🔂 Installing NPM Dependencies..."
npm install

echo "🚮 Deleting Dist Build..."
rm -rf dist

echo "🔄 Updating the .env file with the region"
sed -i "s/AWS_S3_DEFAULT_REGION=.*/AWS_S3_DEFAULT_REGION=$DEPLOY_REGION/" .env
echo "Updated the.env file with the region: $DEPLOY_REGION"

echo "🛠️ Production Build Started..."
npm run build
echo "🟢 Build Success..."

echo "🔃 Managing PM2 Process: $PROCESS_NAME"

# Check if process exists and get its status
PROCESS_INFO=$(sudo -u ubuntu pm2 jlist 2>/dev/null | jq -r --arg name "$PROCESS_NAME" '.[] | select(.name==$name) | {exists: true, status: .pm2_env.status, pid: .pid}' 2>/dev/null || echo '{"exists": false}')
EXISTS=$(echo "$PROCESS_INFO" | jq -r '.exists' 2>/dev/null || echo 'false')

if [ "$EXISTS" = "true" ]; then
    CURRENT_STATUS=$(echo "$PROCESS_INFO" | jq -r '.status' 2>/dev/null || echo 'stopped')
    PID=$(echo "$PROCESS_INFO" | jq -r '.pid' 2>/dev/null)
    
    if [ "$CURRENT_STATUS" = "online" ]; then
        echo "🔄 Process '$PROCESS_NAME' (PID: $PID) is running. Gracefully reloading..."
        sudo -u ubuntu pm2 reload $PROCESS_NAME --update-env
    else
        echo "⚠️  Process '$PROCESS_NAME' exists but is not running. Restarting..."
        sudo -u ubuntu pm2 restart $PROCESS_NAME --update-env
    fi
else
    echo "🚀 Process '$PROCESS_NAME' not found. Starting new instance..."
    # First try to start, if it fails with 'already launched', force restart
    if ! sudo -u ubuntu pm2 start dist/index.js --name "$PROCESS_NAME" 2>/dev/null; then
        echo "⚠️  Process already exists in PM2. Force restarting..."
        sudo -u ubuntu pm2 delete "$PROCESS_NAME" 2>/dev/null || true
        sudo -u ubuntu pm2 start dist/index.js --name "$PROCESS_NAME"
    fi
    sudo -u ubuntu pm2 save
fi

# Verify the process is running
sleep 2
STATUS=$(sudo -u ubuntu pm2 jlist 2>/dev/null | jq -r --arg name "$PROCESS_NAME" '.[] | select(.name==$name) | .pm2_env.status' 2>/dev/null || echo 'not-found')

if [ "$STATUS" = "online" ]; then
    echo "✅ Success: '$PROCESS_NAME' is now running"
else
    echo "❌ Error: Failed to start '$PROCESS_NAME'. Current status: ${STATUS:-not-found}"
    echo "Check logs with: sudo -u ubuntu pm2 logs $PROCESS_NAME"
    exit 1
fi

# echo "🔄 Reloading systemd daemon configuration"
# sudo systemctl daemon-reload
# echo "🚀 Restarting Nginx"
# sudo systemctl restart nginx
# echo "✅ Web Nginx Server Restarted"
