function getBucketKeyfromS3Path(s3Path:string){
    if (s3Path.startsWith('/')) {
        s3Path = s3Path.substring(1);
    }
    const firstSlashIndex = s3Path.indexOf('/');
    if (firstSlashIndex === -1) {
        throw new Error("Invalid S3 path format");
    }

    const bucket = s3Path.substring(0, firstSlashIndex);
    const key = s3Path.substring(firstSlashIndex + 1);

    return { bucket, key };
}


const s3Path = "/annotated-songs-wav/Funk_1724925135/66d044cf163911f63e58f7d5_98_A.wav";
const { bucket, key } = getBucketKeyfromS3Path(s3Path);
console.log(bucket, key);