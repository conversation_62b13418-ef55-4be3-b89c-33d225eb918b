import crypto from 'crypto'
import { Pagination } from '../models/Interface';
import path from 'path';
import fs from 'fs';
import _ from "lodash";
import { Types } from "mongoose";

export default class Utils {

    static escapeChar(str: string): string {
        str = str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        return str
    }

    static get apiReqId(): string {
        const now = Date.now()
        const base36Ts = now.toString(28);
        const rev = now.toString().slice(-7)
        let digits = 'FAB54CD67EA13'
        let id = ''
        for (let i = 0; i < 5; i++) { id += digits[Math.floor(Math.random() * 8)] }
        return `${id}-${rev}-${base36Ts}`.toUpperCase();
    }

    static sleep = async (mili: number): Promise<void> => {
        if (mili > 0)
            await new Promise(f => setTimeout(f, mili));
    };

    static generateUserIdhash(email: string) {
        return crypto.createHash('sha256').update(email).digest('hex');
    }

    static removeUndefinedProps(obj: any) {
        for (let prop in obj) {
            if (obj.hasOwnProperty(prop) && obj[prop] === undefined) {
                delete obj[prop];
            }
        }
    }

    public static getPagination(params: { limit: number, skip: number, docLength: number }) {
        const page: Pagination = {
            next: true,
            previous: false,
            content_length: params.docLength,
        }

        if (params.skip > 0) page.previous = true;
        if (params.docLength < params.limit) page.next = false
       
        Utils.removeUndefinedProps(page)
        page.skip =  params.skip;
        page.limit = params.limit;
        return page
    }

    public static normalizeSongTitle(str: string) {
        let normalized = str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        normalized = normalized.replace(/[^a-zA-Z0-9]/g, ' ')
        normalized = normalized.replace(/\s+/g, ' ')
        return normalized.toLowerCase().trim()
    }

    static groupBy(key: string, arr: any[]) {
        return arr.reduce(
            (result: any, item: any) => ({
                ...result,
                [item[key]]: [
                    ...(result[item[key]] || []),
                    item,
                ],
            }),
            {},
        );
    };

    public static shuffleArray(array: any[]) {
        const shuffledArray = [...array];
        for (let i = shuffledArray.length - 1; i > 0; i--) {
            const randomIndex = Math.floor(Math.random() * (i + 1));
            [shuffledArray[i], shuffledArray[randomIndex]] = [shuffledArray[randomIndex], shuffledArray[i]];
        }
        return shuffledArray;
    }

    public static getProjectInfo() {
        const packageJsonPath = path.join(__dirname, '../../package.json');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
        return { version: packageJson.version, name: packageJson.name };
    }

    public static deepCloneWithObjectId<T>(obj: T): T {
        return _.cloneDeepWith(obj, (value) => {
          if (Types.ObjectId.isValid(value) && value instanceof Types.ObjectId) {
            return value; // Return ObjectId as-is
          }
          return undefined; // Use default clone for everything else
        });
      }

    // static groupByGenre(songs: any[]): Record<string, AnnotatedSongBean[]> {
    //     return songs.reduce((acc, song) => {
    //         if (!acc[song?.genre]) {
    //             acc[song?.genre] = [];
    //         }
    //         acc[song?.genre].push(song);
    //         return acc;
    //     }, {} as Record<string, AnnotatedSongBean[]>);
    // }

}