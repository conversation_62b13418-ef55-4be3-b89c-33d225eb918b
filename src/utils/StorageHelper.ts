import * as AWS from "@aws-sdk/client-s3";
import { Env } from "../Env";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";


const DEFAULT_REGION = Env.aws_s3_default_region as AWSRegion;

export enum AWSRegion {
    ap_south_1 = "ap-south-1",
    us_west_1 = "us-west-1"
}   

// Create S3 clients for both regions
const createS3Client = (region: string) => new AWS.S3Client({
    region,
    credentials: {
        accessKeyId: Env.aws_s3_access_key_id,
        secretAccessKey: Env.aws_s3_secret_access_key,
    },
});

const S3_CLIENTS = {
    [AWSRegion.ap_south_1]: createS3Client(AWSRegion.ap_south_1),
    [AWSRegion.us_west_1]: createS3Client(AWSRegion.us_west_1)
};

const S3_CLIENT = S3_CLIENTS[DEFAULT_REGION];

function getBucketForRegion(bucket: string, region: string): string {
    if (region === AWSRegion.us_west_1 && !bucket.endsWith('-us')) {
        return `${bucket}-us`;
    }
    return bucket;
}

export const S3BucketName = {
    annotated_song_wav: getBucketForRegion("annotated-songs-wav", DEFAULT_REGION),
    annotated_song_wav_cache: getBucketForRegion("annotated-songs-wav-cache", DEFAULT_REGION),
    ttl_24hrs: getBucketForRegion("melodyze-24hrs-ttl", DEFAULT_REGION),
    final_recording: getBucketForRegion("user-final-recording", DEFAULT_REGION),
} as const;

class AWS_S3 {

    async upload(rawFile: Express.Multer.File, options: { Bucket: string, key: string }) {
        try {
            const params: AWS.PutObjectRequest = {
                ACL: "private",
                Bucket: getBucketForRegion(options.Bucket, DEFAULT_REGION),
                Key: options.key,
                Body: rawFile.buffer as any,
                ContentType: rawFile.mimetype
            }

            const command = new AWS.PutObjectCommand(params)
            await S3_CLIENT.send(command)
            return params.Key

        } catch (error: any) {
            throw this._throwException(error.message);
        }
    }

    async uploadCustom(file: any, bucket: string, key: string, contentType: string, contentLength: string) {
        try {
            const params: AWS.PutObjectRequest = {
                ACL: "private",
                Bucket: getBucketForRegion(bucket, DEFAULT_REGION),
                Key: key,
                Body: file,
                ContentType: contentType,
                ContentLength: Number(contentLength)
            }

            const command = new AWS.PutObjectCommand(params)
            await S3_CLIENT.send(command)
            return `/${bucket}/${key}`

        } catch (error: any) {
            throw this._throwException(error.message);
        }
    }

    async getObject(bucket: string, key: string) {
        try {
            const params: AWS.GetObjectRequest = {
                Bucket: getBucketForRegion(bucket, DEFAULT_REGION),
                Key: key,
            }
            const command = new AWS.GetObjectCommand(params)
            return await S3_CLIENT.send(command)

        } catch (error: any) {
            throw this._throwException(error.message);
        }
    }

    async isObjectExists(bucket: string, key: string) {
        try {
            const params: AWS.HeadObjectRequest = {
                Bucket: getBucketForRegion(bucket, DEFAULT_REGION),
                Key: key,
            }
            const command = new AWS.HeadObjectCommand(params)
            await S3_CLIENT.send(command)
            return true

        } catch (error: any) {
            if (error.name === 'NotFound') {
                return false;
            }
            throw error;
        }
    }

    /**
     * @param bucket S3BucketName
     * @param key String
     * @param ttl Number (in seconds)
     */
    async getSignedUrl(path: string, ttl: number = 3600) {
        try {
            let { bucket, key } = S3Utils.getBucketKeyfromS3Path(path)
            const command = new AWS.GetObjectCommand({
                Bucket: getBucketForRegion(bucket, DEFAULT_REGION),
                Key: key
            })
            return await getSignedUrl(S3_CLIENT, command, { expiresIn: ttl });
        } catch (error: any) {
            throw this._throwException(error.message);
        }
    }


    async putSignedUrl(bucket: string, key: string, contentType: string, ttl: number = 600) {
        try {
            const command = new AWS.PutObjectCommand({
                Bucket: getBucketForRegion(bucket, DEFAULT_REGION),
                Key: key,
                ContentType: contentType
            })
            return await getSignedUrl(S3_CLIENT, command, { expiresIn: ttl });
        } catch (error: any) {
            throw this._throwException(error.message)
        }
    }

    async copyObject(sourceBucket: string, sourceKey: string, destBucket: string, destKey: string) {
        try {
            const command = new AWS.CopyObjectCommand({
                CopySource: `${getBucketForRegion(sourceBucket, DEFAULT_REGION)}/${sourceKey}`,
                Bucket: getBucketForRegion(destBucket, DEFAULT_REGION),
                Key: destKey
            })
            await S3_CLIENT.send(command);
            return true
        } catch (error: any) {
            throw this._throwException(error.message);
        }
    }

    private _throwException(message: any) {
        console.error("AWS_S3:", message)
        throw new Error(message);
    }

}


export class S3Utils {
    public static getBucketKeyfromS3Path(s3Path: string): { bucket: string, key: string } {
        if (s3Path.startsWith('/')) {
            s3Path = s3Path.substring(1);
        }
        const firstSlashIndex = s3Path.indexOf('/');
        if (firstSlashIndex === -1) {
            throw new Error("Invalid S3 path format");
        }

        const bucket = s3Path.substring(0, firstSlashIndex);
        const key = s3Path.substring(firstSlashIndex + 1);

        return { bucket, key };
    }
}



const awsS3 = new AWS_S3()
export default awsS3