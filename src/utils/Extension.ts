import { HTTPCode } from "../Dict";
import { Pagination, Success } from "../models/Interface";


export class ErrorResponse extends Error {
    message: string;
    status: number;
    code?: string
    detail?: any

    constructor(message: string, status: number, code?: string, detail?: any) {
        super(message)
        this.message = message
        this.status = status
        this.code = code
        this.detail = detail
    }

    static $400(message: string, code?: string): ErrorResponse {
        return new ErrorResponse(message, HTTPCode.$400, code)
    }

    static $401(code: string): ErrorResponse {
        return new ErrorResponse("Error: Invalid authorization token", HTTPCode.$401, `UNATHC-${code}`)
    }


    static $404(attr: string, code?: string): ErrorResponse {
        return new ErrorResponse(`${attr} not found`, HTTPCode.$404, code)
    }

    static $404C(message: string, code?: string): ErrorResponse {
        return new ErrorResponse(message, HTTPCode.$404, code)
    }

    static get $500(): ErrorResponse {
        return new ErrorResponse("Internal Server Error: Please try after some time", HTTPCode.$500, "SRVR-500")
    }
    static $500E(error: any): ErrorResponse {
        return new ErrorResponse("Internal Server Error: Please try after some time", HTTPCode.$500, "SRVR-500", error)
    }

    static $500C(message: string, code?: string): ErrorResponse {
        return new ErrorResponse(message, HTTPCode.$500, code)
    }

    static get path_not_found(): ErrorResponse {
        return new ErrorResponse("Path not found", HTTPCode.$404, "ERR-404")
    }

}



export class SuccessResponse implements Success {
    message: string;
    status: number;
    data?: any;
    pagination?: Pagination;

    constructor(message: string, status: number, data?: any, pagination?: Pagination) {
        this.message = message;
        this.status = status;
        this.data = data;
        this.pagination = pagination;
    }


    static $200S(data: any, pagination?: Pagination) {
        return new SuccessResponse('success', HTTPCode.$200, data, pagination)
    }

    static $200(message: string, data: any, pagination?: Pagination) {
        return new SuccessResponse(message, HTTPCode.$200, data, pagination)
    }

    static $201(attr: string, data: any) {
        return new SuccessResponse(`Success! You created ${attr}`, HTTPCode.$201, data)
    }

    static $201S(data: any) {
        return new SuccessResponse(`success`, HTTPCode.$201, data)
    }

    static $201C(message: string, data: any) {
        return new SuccessResponse(message, HTTPCode.$201, data)
    }

    static $202(attr: string, data: any) {
        return new SuccessResponse(`Success! You updated ${attr}`, HTTPCode.$202, data)
    }

    static $202C(message: string, data: any) {
        return new SuccessResponse(`Success! ${message}`, HTTPCode.$202, data)
    }

    static get $204() {
        return new SuccessResponse("", HTTPCode.$204)
    }

}



// class CustomError extends Error {
//     constructor(message?: string) {
//         super(message); // 'Error' breaks prototype chain here
//         this.name = 'CustomError';
//         Object.setPrototypeOf(this, new.target.prototype); // restore prototype chain
//     }
// }