import { MulticastMessage } from 'firebase-admin/lib/messaging/messaging-api';
import firebaseAdmin from '..'
import { PNDeviceInfo } from '../db/models/UserMaster';


export enum FCMType {
    song_checkout_cron = "song_checkout_cron"
}

interface PNPayload {
    title: string;
    body: string;
    imageUrl?: string;
}

interface PNAction {
    action?: string;
    title?: string;
}

export interface PNOptions {
    token_list?: PNDeviceInfo[];
    payload: PNPayload;
    action: PNAction;
    data?: {
        fcm_type: FCMType,
        [key: string]: any
    };
}



class PNHelper {
    sendRichPN = async (options: PNOptions) => {
        const { token_list, payload, action, data } = options;

        let tokens = token_list?.map(d => d.fcm_token).filter((t): t is string => !!t) || []
        if (!tokens.length) return

        try {
            const message: MulticastMessage = {
                notification: {
                    title: payload.title,
                    body: payload.body,
                    imageUrl: payload.imageUrl,
                },
                android: {
                    notification: {
                        title: payload.title,
                        body: payload.body,
                        imageUrl: payload.imageUrl,
                        clickAction: undefined,
                        icon: payload.imageUrl,
                        color: undefined,
                        sound: undefined,
                        tag: undefined,
                        channelId: undefined,
                        ticker: undefined,
                        sticky: undefined,
                        vibrateTimingsMillis: undefined,
                        defaultSound: undefined,
                        defaultLightSettings: undefined,
                        visibility: undefined,
                    },
                    priority: "high"
                },
                apns: {
                    payload: {
                        aps: {
                            alert: {
                                title: payload.title,
                                body: payload.body,
                                subtitle: undefined,
                                launchImage: payload.imageUrl,

                            },
                            sound: {
                                critical: false,
                                name: "default",
                                volume: 1,
                            },
                            category: data?.fcm_type,
                            threadId: undefined,
                            mutableContent: true,
                            'content-available': 1,
                        },
                        //custom
                        imageUrl: payload.imageUrl, // For APNs (iOS) notifications
                    },
                },
                data: data || {},
                tokens
            };

            const response = await firebaseAdmin.messaging().sendEachForMulticast(message);
            console.log('Rich notifications sent:', `${response.successCount}/${tokens.length}`);
            if (response.failureCount > 0) {
                response.responses.filter(r => !r.success).forEach(e => { console.error("Rich notifications failed:", e.error?.code, e.error?.message) });
            }
            return response;
        } catch (error: any) {
            console.error('Error sending notification:', error.message);
        }
    };

}


const pnHelper = new PNHelper()
export default pnHelper