export { };
import { readFileSync } from 'fs';
import { join } from 'path';

const getVersion = (): string => {
    const packageJsonPath = join(__dirname, "../../", 'package.json');
    const packageJsonContent = readFileSync(packageJsonPath, 'utf-8');
    const packageJson = JSON.parse(packageJsonContent);
    return packageJson.version;
};

declare global {
    interface Array<T> {
        first(): T | undefined;
        last(): T | undefined;
    }
    var projectVersion: string;

};

global.projectVersion = getVersion()

Array.prototype.first = function () {
    return this.length > 0 ? this[0] : undefined;
};

Array.prototype.last = function () {
    return this.length > 0 ? this[this.length - 1] : undefined;
};



['log', 'warn', 'error', 'info'].forEach((methodName) => {
    const originalMethod = (console as any)[methodName];
    (console as any)[methodName] = (...args: any) => {
        let initiator = 'unknown place';
        try {
            throw new Error();
        } catch (e: any) {
            if (typeof e.stack === 'string') {
                let isFirst = true;
                for (const line of e.stack.split('\n')) {
                    const matches = line.match(/^\s+at\s+(.*)/);
                    if (matches) {
                        if (!isFirst) { // first line - current function
                            // second line - caller (what we are looking for)
                            const ab = matches[1].split('/');
                            initiator = ab[ab.length - 1]
                            break;
                        }
                        isFirst = false;
                    }
                }
            }
        }

        const tag = methodName === 'error'
            ? '🟥 MZ_ERROR •'
            : methodName === 'warn'
                ? '🟧 MZ_WARN •'
                : methodName === 'info'
                    ? '🟦 •'
                    : '🟩 •';
        let timestamp = new Date().toLocaleString('en-GB', { timeZone: 'Asia/Kolkata', hour12: false })
        originalMethod.apply(console, [timestamp, '•', projectVersion, '•', tag, ...args, '•', `${initiator}`]);
    };
});
