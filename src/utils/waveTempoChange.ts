import {
    S3Client,
    GetObjectCommand,
    PutObjectCommand,
} from "@aws-sdk/client-s3";
import fs from "fs";
import path from "path";
import { pipeline } from "stream/promises";
import { Env } from "../Env";
import { spawnSync } from 'child_process'
import { S3BucketName } from "./StorageHelper";


const s3 = new S3Client({
    region: Env.aws_s3_default_region,
    credentials: {
        accessKeyId: Env.aws_s3_access_key_id,
        secretAccessKey: Env.aws_s3_secret_access_key,
    },
});

export class WavTempoChange {
    static async handler(
        inputKey: string,
        outputKey: string,
        original_tempo: number,
        user_tempo: number
    ) {
        try {
            const inputBucket = S3BucketName.annotated_song_wav;
            const outputBucket = S3BucketName.annotated_song_wav_cache;
            const timeStretchFactor = user_tempo / original_tempo;

            const tempDir = path.join(__dirname, `../../temp/${Date.now()}`);
            const inputFilePath = path.join(tempDir, "input.wav");
            const outputFilePath = path.join(tempDir, "output.wav");

            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
                console.log(`Directory created: ${tempDir}`);
            }

            const inputData = await s3.send(
                new GetObjectCommand({
                    Bucket: inputBucket,
                    Key: inputKey,
                })
            );

            // Write the file to /tmp/input.wav
            const writeStream = fs.createWriteStream(inputFilePath);
            if (inputData.Body) {
                await pipeline(inputData.Body as any, writeStream);
            } else {
                throw new Error("Input data body is undefined");
            }

            const result = spawnSync('rubberband', [
                '-T', timeStretchFactor.toString(),
                inputFilePath,
                outputFilePath
            ]);

            if (result.error) {
                console.error('Error:WavechangeTempoRubberband:', inputFilePath, result.error);
            }
            console.log('WavechangeTempoRubberband: success', result.stdout.toString());
            const processedFileData = fs.readFileSync(outputFilePath);

            await s3.send(
                new PutObjectCommand({
                    Bucket: outputBucket,
                    Key: outputKey,
                    Body: processedFileData,
                    ContentType: "audio/wav",
                })
            );

            fs.rmSync(tempDir, { recursive: true, force: true });
            console.log("Temporary directory removed:", tempDir);

            return {
                outputFileKey: outputKey,
                inputKey,
                original_tempo,
                user_tempo,
            };
        } catch (error) {
            console.error("An error occurred:", error);
            throw error;
        }
    }
}