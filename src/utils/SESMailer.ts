import * as AWS from "@aws-sdk/client-sesv2";
import { Env } from '../Env';


const SES_CONFIG: AWS.SESv2ClientConfig = {
    region: Env.aws_s3_default_region,
    credentials: {
        accessKeyId: Env.aws_s3_access_key_id,
        secretAccessKey: Env.aws_s3_secret_access_key,
    },
};

const SES_CLIENT = new AWS.SESv2(SES_CONFIG);


export enum MailSenderAddress {
    MR_MELO = "<EMAIL>"
}

export enum AdminEmail {
    animesh = "<EMAIL>",
    debayan = "<EMAIL>",
    arghya = "",
    abhishek = ""
}


export interface SendMailParams {
    to: string | string[]
    subject: string
    body: string
    cc?: string | string[]
    bcc?: string | string[]
}

interface SendMailParamsPrivate extends SendMailParams {
    source_alias: string
    from: string
}

function createSESEmailCommandInput(args: SendMailParamsPrivate): AWS.SendEmailCommandInput {
    args.to = (typeof args.to === 'string') ? [args.to] as string[] : args.to as string[];
    args.bcc = (typeof args.bcc === 'string') ? [args.bcc] as string[] : args.bcc as string[];
    args.cc = (typeof args.cc === 'string') ? [args.cc] as string[] : args.cc as string[];

    const mailParams: AWS.SendEmailCommandInput = {
        FromEmailAddress: `${args.source_alias} <${args.from}>`,
        Destination: {
            ToAddresses: args.to,
            CcAddresses: args.cc,
            BccAddresses: args.bcc
        },
        ReplyToAddresses: [args.from!],
        Content: {
            Simple: {
                Body: {
                    Html: {
                        Charset: 'UTF-8',
                        Data: args.body,
                    },
                },
                Subject: {
                    Charset: 'UTF-8',
                    Data: args.subject,
                }
            }
        }
    }

    return mailParams
}


export class SESMailer {
    private static instance: SESMailer;
    private constructor() { }

    static getInstance(): SESMailer {
        if (!SESMailer.instance) {
            SESMailer.instance = new SESMailer();
        }
        return SESMailer.instance;
    }

    private async send(args: SendMailParamsPrivate): Promise<boolean> {
        try {
            const sendCommand = new AWS.SendEmailCommand(createSESEmailCommandInput(args))
            const MessageId = (await SES_CLIENT.send(sendCommand)).MessageId
            console.info(`SESMailer.send: ${args.to} ‣ ${args.subject} : ${MessageId}`)
            return true
        } catch (error: any) {
            console.error(`SESMailer.send: ${args.to} ‣ ${args.subject} : ${error.message}`)
            return false
        }
    }

    async fromMrMelo(args: SendMailParams) {
        await this.send({ ...args, from: MailSenderAddress.MR_MELO, source_alias: "Melodyze" })
    }
}


// function createSESEmailAttachmentCommand(args: SendMailParams) {
//     args.to = (typeof args.to === 'string') ? [args.to] as string[] : args.to as string[];
//     args.bcc = (typeof args.bcc === 'string') ? [args.bcc] as string[] : args.bcc as string[];
//     args.cc = (typeof args.cc === 'string') ? [args.cc] as string[] : args.cc as string[];

//     const mailParams: AWS.SendEmailCommandInput = {
//         FromEmailAddress: `${args.source_alias} <${args.from}>`,
//         Destination: {
//             ToAddresses: args.to,
//             CcAddresses: args.cc,
//             BccAddresses: args.bcc
//         },
//         ReplyToAddresses: [],
//         Content: {
//             Raw: {
//                 Data: []
//             }
//         }
//     }

//     return mailParams
// }