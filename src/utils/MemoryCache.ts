import { LRUCache } from 'lru-cache';

interface MemoryCacheOptions {
    max: number;
    ttl: number;
    maxSize?: number;
    allowStale?: boolean;
    updateAgeOnGet?: boolean;
    updateAgeOnHas?: boolean;
    ttlAutopurge?: boolean;
    sizeCalculation?: (value: any, key: string) => number;
}

export interface IMemoryCache {
    /** ttl - in milliseconds */
    set<T>(key: string, value: T, options?: { ttl?: number }): void;
    get<T>(key: string): T | null;
    has(key: string): boolean;
    /** delete a specific key */
    delete(key: string): boolean;
    /** clear all cache */
    clear(): void;
    size(): number;
    keys(): string[];
    values(): any[];
    /** Get remaining TTL of a specific key */
    getRemainingTTL(key: string): number | undefined;
}

class MemoryCache implements IMemoryCache {
    private cache: LRUCache<string, any>;
    private readonly ttlms = 24 * 60 * 60 * 1000;

    constructor() {
        const defaultOptions: MemoryCacheOptions = {
            max: 10000,
            ttl: this.ttlms,
            maxSize: 10 * 1024 * 1024,
            allowStale: false,
            updateAgeOnGet: true,
            updateAgeOnHas: false,
            ttlAutopurge: true,
            sizeCalculation: (value, key) => 1,
        };
        this.cache = new LRUCache(defaultOptions);
    }

    set(key: string, value: any, options?: { ttl?: number }): void {
        this.cache.set(key, value, options);
    }

    get(key: string): any {
        return this.cache.get(key);
    }

    has(key: string): boolean {
        return this.cache.has(key);
    }

    delete(key: string): boolean {
        return this.cache.delete(key);
    }

    clear(): void {
        this.cache.clear();
    }

    size(): number {
        return this.cache.size;
    }

    keys(): string[] {
        return [...this.cache.keys()] as string[];
    }

    values(): any[] {
        return [...this.cache.values()];
    }

    getRemainingTTL(key: string): number | undefined {
        return this.cache.getRemainingTTL(key);
    }
}

const MemoryCacheSingleton = new MemoryCache();
export default MemoryCacheSingleton;