import axios from "axios";
import { WebSignupFromBean } from "../db/models/MainWebsite";
import awsS3, { S3BucketName } from "./StorageHelper";


class DiscordWrapper {
    private readonly mr_melo_url = "https://discord.com/api/webhooks/1312645762072576080/OA8T264n6-Zz6UMnmXpAYiRgKOuyfmG9Wp3ChTOCiqqinzHFhU53Iim9kLlMBylyJkVN"
    private readonly web_signup_url = "https://discord.com/api/webhooks/1316658569223077888/FLg5vJEof7YTDcT5AeNcfmJddhWaVNVA5myFqG9A54TQPqSWcWP46HaFJwdwKdrz6ccZ"

    async send_message_test() {
        try {
            await axios.post(this.mr_melo_url, {
                "content": "Hello, `World!` ",
                "tts": false,
                "embeds": [{
                    "title": "Hello, Embed!",
                    "description": "This is an embedded message."
                }, {
                    "title": "Hello, Embed 2!",
                    "description": "This is an embedded message 2."
                }]
            }
            )

        } catch (error: any) {
            console.error("send_message_test:", error.message);
        }
    }

    async new_web_form_signup(form: WebSignupFromBean) {
        try {

            let audio_url;
            if (form.uploaded_audio_path) {
                audio_url = await awsS3.getSignedUrl(form.uploaded_audio_path)
            }
            await axios.post(this.web_signup_url, {
                "content": `🔔 Alert! New signup from web.`,
                "tts": false,
                "embeds": [
                    {
                        "title": `${form.first_name} just signed up from web.`,
                        "description": `\`\`\`Name: ${form.first_name}\nEmail: ${form.email}\nPhone: ${form.phone || ""}\nSocial Link: ${form.social_media_link || ""}\nHelp Asked: ${form.help_asked || ""}\nFound Us: ${form.find_us_via || ""}\`\`\``
                    },
                    // {
                    //     "type": "link",
                    //     "description": audio_url || form.shared_audio_url || ""
                    // }
                ],

                // "attachments": [
                //     {
                //         "id": "0",
                //         "filename": "melo.mp4",
                //         "url": "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/website_videoes/promo_video_1080p.mp4",
                //         "size": 5883384,
                //         "proxy_url": "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/website_videoes/promo_video_1080p.mp4",
                //     }
                // ]
            })

        } catch (error: any) {
            console.error("new_web_form_signup:", error.response.data);
        }
    }
}

const discordWrapper = new DiscordWrapper()
export default discordWrapper