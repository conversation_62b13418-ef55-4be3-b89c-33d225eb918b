import { JwtPayload } from "jsonwebtoken"


export interface MeloJwtPayload extends JwtPayload{
    _id?: string
    email?: string
    name?: string
}
export interface EnvModel {
    port: number
    firebase_realtime_db_uri: string
    aws_s3_default_region: string
    aws_s3_access_key_id: string
    aws_s3_secret_access_key: string
    oauth2_client_id: string
    oauth2_client_secret: string
    jwt_signature: string
    mongodb_url: string
}

export interface Pagination {
    skip?: number
    limit?: number
    next?: boolean
    previous?: boolean
    content_length: number
}

export interface Success {
    message: string;
    status: number;
    data?: any;
    pagination?: Pagination;
}

export interface SuccessConstructor {
    new(message: string, status: number, data?: any, pagination?: Pagination): Success;
    (message: string, status: number, data?: any, pagination?: Pagination): Success;
    readonly prototype: Success;
}

declare var Success: SuccessConstructor;



export interface KeyCondExpr {
    pk: string
    pv: string
    sk?: string
    sv?: string | number
}