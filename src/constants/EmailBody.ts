import { WebSignupFromBean } from "../db/models/MainWebsite";

export class EmailBody {
  static webSignupWelcome(username: string) {
    return `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f9f9f9; color: #333; line-height: 1.6;">
    <table align="center" border="0" cellpadding="0" cellspacing="0" width="800" style="border-collapse: collapse; background-color: #ffffff; border: 1px solid #dddddd; margin-top: 20px; border-radius: 12px; overflow: hidden;">
        <tr>
        <td style="padding: 20px; text-align: center; background-color: #282b35; color: #f9f8f8; font-size: 24px; font-weight: bold;">
          Welcome to Melodyze! 🎵
        </td>
    </tr>
      <tr>
        <td style="padding: 20px;">
          <p style="margin: 0 0 20px; font-size: 16px;">
            Hi ${username},
          </p>
          <p style="margin: 0 0 20px; font-size: 16px;">
            We’re excited to have you as an early access member.
          </p>
          <p style="margin: 0 0 20px; font-size: 16px;">
            As we gear up to launch, we’d love to get to know you better! If you have any recordings or a link showcasing your singing or musical skills, feel free to share them with us. It would help us tailor the experience to better suit your needs.
          </p>
          <p style="margin: 0 0 20px; font-size: 16px; font-weight: bold;">
            📩 Simply reply to this email with your recordings or social media/music profile link.
          </p>
          <p style="margin: 0 0 20px; font-size: 16px;">
            We’ll be in touch soon with the next steps. Stay tuned!
          </p>
          <p style="margin: 0; font-size: 16px;">Cheers,</p>
          <p style="margin: 0; font-size: 16px; font-weight: bold;">The Melodyze Team 🎶</p>
        </td>
      </tr>
      <tr>
        <td style="text-align: center; padding: 20px; background-color: #e8e8e8; font-size: 12px; color: #666;">
          © 2025 Melodyze. All rights reserved.
        </td>
      </tr>
    </table>
  </body>
</html>
`
  }
  static webSignupAdminNotification(form: WebSignupFromBean) {
    return `<!DOCTYPE html>
<html>
  <head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
  </head>
  <body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f9f9f9; color: #333; line-height: 1.6;\">
    <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"800\" style=\"border-collapse: collapse; background-color: #ffffff; border: 1px solid #dddddd; margin-top: 20px; border-radius: 12px; overflow: hidden;\">
      <tr>
        <td style=\"padding: 20px; text-align: center; background-color: #282b35; color: #f9f8f8; font-size: 24px; font-weight: bold;\">
          New Signup Notification 🎉
        </td>
      </tr>
      <tr>
        <td style=\"padding: 20px;\">
          <p style=\"margin: 0 0 20px; font-size: 16px;\"><strong>A new user has signed up on Melodyze!</strong></p>
          <table style=\"width:100%; font-size: 15px; border-collapse: collapse;\">
            <tr><td style=\"padding: 6px 0; font-weight: bold;\">Name:</td><td style=\"padding: 6px 0;\">${form.first_name}</td></tr>
            <tr><td style=\"padding: 6px 0; font-weight: bold;\">Email:</td><td style=\"padding: 6px 0;\">${form.email}</td></tr>
            ${form.phone ? `<tr><td style=\"padding: 6px 0; font-weight: bold;\">Phone:</td><td style=\"padding: 6px 0;\">${form.phone}</td></tr>` : ''}
            ${form.help_asked ? `<tr><td style=\"padding: 6px 0; font-weight: bold;\">Help Asked:</td><td style=\"padding: 6px 0;\">${form.help_asked}</td></tr>` : ''}
            ${form.find_us_via ? `<tr><td style=\"padding: 6px 0; font-weight: bold;\">Found Us Via:</td><td style=\"padding: 6px 0;\">${form.find_us_via}</td></tr>` : ''}
            ${form.social_media_link ? `<tr><td style=\"padding: 6px 0; font-weight: bold;\">Social Media Link:</td><td style=\"padding: 6px 0;\">${form.social_media_link}</td></tr>` : ''}
            <tr><td style=\"padding: 6px 0; font-weight: bold;\">Signup Time:</td><td style=\"padding: 6px 0;\">${new Date(form.created_at).toLocaleString()}</td></tr>
          </table>
        </td>
      </tr>
      <tr>
        <td style=\"text-align: center; padding: 20px; background-color: #e8e8e8; font-size: 12px; color: #666;\">
          © 2025 Melodyze. All rights reserved.
        </td>
      </tr>
    </table>
  </body>
</html>`;
  }
}