import './utils/Prototype';
import express, { Request, Response } from "express";
import compression from 'compression';
import cors, { CorsOptions } from "cors";
import multer from 'multer'
import { path_not_found, post_logger_final_response, pre_logger, respLog } from './middlewares/logger';
import firebaseAdmin from 'firebase-admin';
import { Env } from './Env';
import serviceAccount from './melodyze-65923-service.json'
import { connectMongoDB } from './db/connection';
import auth_router from './api-services/auth/router';
import song_router from './api-services/song/router';
import user_router from './api-services/user/router';
import utilities_router from './api-services/utilities/router';
import website_router from './api-services/main-website/router';
import { auth_validation } from './middlewares/Authentication';
import Utils from './utils/Utils';

require('dotenv').config();

const app = express();
const PORT = Env.port;

connectMongoDB().then()

firebaseAdmin.initializeApp({
    credential: firebaseAdmin.credential.cert(serviceAccount as firebaseAdmin.ServiceAccount),
    databaseURL: "https://melodyze-65923-default-rtdb.firebaseio.com"
});

export default firebaseAdmin;

const corsOptions: CorsOptions = {
    origin: "*",
    optionsSuccessStatus: 200,
    exposedHeaders: ["x-api-request-id"],
};

app.use(compression());
app.use(express.json())
app.use(express.urlencoded({ extended: true }));
app.use(multer().any())
app.use(cors(corsOptions));

app.use(pre_logger)

app.get("/health_check", async (req: any, res: any) => {
    respLog(req)
    const { version, name } = Utils.getProjectInfo()
    return res.status(200).send({ success: true, message: "Ok", version, name })
});


app.use('/auth', auth_router)
app.use('/song', auth_validation, song_router)
app.use('/user', auth_validation, user_router)
app.use('/utilities', utilities_router)
app.use('/web', website_router)

app.use('/*', path_not_found)

app.use(post_logger_final_response)


process.on("uncaughtException", async function (err) {
    console.error("uncaughtException:", err.message)
    process.exit(1);
});

const server = app.listen(PORT, () => {
    console.log(`✅ Server Listening on port: ${PORT}`);

});

server.on('close', (err: any) => {
    console.warn("Shutting down server:", err)
})

export const testImport = "";