import { EnvModel } from "./models/Interface";
require('dotenv').config();


// export class Environment implements EnvModel {
//    readonly firebase_realtime_db_uri = process.env.FIREBASE_REALTIME_DB_URI!
//    readonly aws_ddb_default_region = process.env.AWS_DDB_DEFAULT_REGION!
//    readonly aws_ddb_access_key_id = process.env.AWS_DDB_ACCESS_KEY_ID!
//    readonly aws_ddb_secret_access_key = process.env.AWS_DDB_SECRET_ACCESS_KEY!
//    readonly aws_s3_default_region = process.env.AWS_S3_DEFAULT_REGION!
//    readonly aws_s3_access_key_id = process.env.AWS_S3_ACCESS_KEY_ID!
//    readonly aws_s3_secret_access_key = process.env.AWS_S3_SECRET_ACCESS_KEY!
//    readonly oauth2_client_id = process.env.OAUTH2_CLIENT_ID!
//    readonly oauth2_client_secret = process.env.OAUTH2_CLIENT_SECRET!
//    readonly jwt_signature = process.env.JWT_SIGNATURE!
// }

export class Environment implements EnvModel {
   readonly port = Number(process.env.PORT) || 8002
   readonly firebase_realtime_db_uri = "https://melodyze-test-default-rtdb.us-central1.firebasedatabase.app"
   readonly aws_s3_default_region = process.env.AWS_S3_DEFAULT_REGION || "ap-south-1"
   readonly aws_s3_access_key_id = "********************"
   readonly aws_s3_secret_access_key = "MEDHmUMfPlrcLXMOVXhM8kwhB6mqwqBpQTX2NKcj"
   readonly oauth2_client_id = "959993156379-b8pa5tn46ie78qkiiam89hhk6emub1cr.apps.googleusercontent.com"
   readonly oauth2_client_secret = "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"
   readonly jwt_signature = "gq54GW65&5_fng4hg4w"
   readonly mongodb_url = "mongodb+srv://app-test:<EMAIL>/app_master"
   readonly RECSYS_UNIVERSAL_USER_ID = "67e052989bff88357c6565ca"

}

export const Env = new Environment();
