import { Router } from 'express'
import { auth_validation } from '../../middlewares/Authentication'
import utilitiesController from './controller'

/**
 * '/utilities'
 */
const utilities_router = Router()


utilities_router.put('/v1/s3_presigned_url/:destiny', auth_validation, utilitiesController.put_s3_presigned_url_api)

//crons
utilities_router.get('/v1/cron_jobs/song_checkout_pn', utilitiesController.song_checkout_pn_cron)
utilities_router.post('/v1/cron_jobs/song_checkout_pn', utilitiesController.song_checkout_pn_cron)
utilities_router.post('/v1/test', utilitiesController.test)
utilities_router.get('/alpha/feeds/:url_signer', utilitiesController.get_feeds_alpha)
utilities_router.patch('/flush_imem_cache', utilitiesController.flush_memory_cache)

export default utilities_router