import { Request, Response, NextFunction } from "express";
import { ErrorResponse, SuccessResponse } from "../../utils/Extension";
import awsS3, { S3BucketName } from "../../utils/StorageHelper";
import ConfigModel, { ConfigType } from "../../db/models/ConfigModel";
import SongMasterModel, { SongLanguage, SongMasterBean, SongStatus } from "../../db/models/SongMaster";
import UserModel, { PNDeviceInfo, PNDeviceType } from "../../db/models/UserMaster";
import pnHelper, { FCMType } from "../../utils/PNHelper";
import MemoryCacheSingleton from "../../utils/MemoryCache";
import CoverFeedModel, { CoverFeedType } from "../../db/models/CoverFeed";


class UtilitiesController {

    async test(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ": test_api :"
        try {
            let key = req.body.key
            const getRemainingTTL = MemoryCacheSingleton.getRemainingTTL(key)
            MemoryCacheSingleton.delete(key)
            // MemoryCacheSingleton.clear()
            const size = MemoryCacheSingleton.size()
            const cache = MemoryCacheSingleton.get(key)
            const all = MemoryCacheSingleton.keys()
            return next(SuccessResponse.$200S({ cache, getRemainingTTL, size, all }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500E(error))
        }
    };

    async put_s3_presigned_url_api(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":put_signed_url_api:"
        try {
            const params = {
                user_id: (req as any)._embed._id?.toString(),
                filename: req.body.filename,
                mimetype: req.body.mimetype as string// required 
            }

            let fileExt = params.mimetype.split("/")[1]
            let filename = params.filename || `${Date.now}.${fileExt}`
            let bucket = ""
            let key = ""


            switch (req.params.destiny) {
                case "denoised_vocal_cache":
                    bucket = S3BucketName.ttl_24hrs
                    key = `denoised_raw_vocal_audio/${params.user_id}/${filename}`
                    break;
                case "denoised_vocal":
                    bucket = S3BucketName.final_recording
                    key = `denoised_raw_vocal_audio/${params.user_id}/${filename}`
                    break;
                case "raw_vocal":
                    bucket = S3BucketName.final_recording
                    key = `raw_vocal_audio/${params.user_id}/${filename}`
                    break;

                case "final_mixed_audio":
                    bucket = S3BucketName.final_recording
                    key = `final_mixed_audio/${params.user_id}/${filename}`
                    break;

                case "filtered_raw_vocal_audio":
                    bucket = S3BucketName.final_recording
                    key = `filtered_raw_vocal_audio/${params.user_id}/${filename}`
                    break;

                case "raw_vocal_cache":
                    bucket = S3BucketName.ttl_24hrs
                    key = `raw_vocal_audio/${params.user_id}/${filename}`
                    break;


                default:
                    return next(ErrorResponse.$400("no destiny found."));
            }

            const url = await awsS3.putSignedUrl(bucket, key, params.mimetype)
            return next(SuccessResponse.$200S({ url, path: `/${bucket}/${key}`, method: "put" }))

        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    }


    async song_checkout_pn_cron(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":song_checkout_pn_cron:"
        next(SuccessResponse.$200S({ sucess: true }))
        try {
            const configs = await ConfigModel.find({ type: { $in: [ConfigType.NOTIFIED_SONG_ID, ConfigType.PUSH_NOTI] } })
            const song_id_config = configs.find(c => c.type === ConfigType.NOTIFIED_SONG_ID)
            const push_noti_config = configs.find(c => c.type === ConfigType.PUSH_NOTI)

            async function pickMasterSong(): Promise<SongMasterBean> {
                const masterSong = await SongMasterModel.aggregate([
                    { $match: { _id: { $nin: song_id_config?.notified_song_ids || [] }, song_status: SongStatus.active, lang: SongLanguage.ENGLISH } },
                    { $sample: { size: 1 } },
                ]);

                if (masterSong.length === 0) {
                    song_id_config!.notified_song_ids = [];
                    await song_id_config?.save()
                    return await pickMasterSong();
                }

                await ConfigModel.updateOne(
                    { _id: song_id_config?._id },
                    { $addToSet: { notified_song_ids: masterSong[0]._id } }
                )
                return masterSong[0] as SongMasterBean;
            }

            async function pickNotificationContent() {
                const content = push_noti_config?.push_noti.contents[push_noti_config.push_noti.sent_index]
                if (content) {
                    const next_sent_index = (push_noti_config.push_noti.sent_index + 1)
                    const pn_length = push_noti_config.push_noti.contents.length
                    await ConfigModel.updateOne(
                        { _id: push_noti_config?._id },
                        { $set: { "push_noti.sent_index": (next_sent_index <= (pn_length - 1) ? next_sent_index : 0) } }
                    )
                }
                return content
            }

            function replaceNotiBody(notiBody: string, song: SongMasterBean): string {
                return notiBody.replace("[[title]]", song.title).replace("[[genre]]", song.default_genre || "Rock")
            }

            function getUniqueDeviceTypesSorted(devices: PNDeviceInfo[]): PNDeviceInfo[] {
                const deviceMap = new Map<PNDeviceType, PNDeviceInfo>();
                // Iterate through the array and keep only the most recent entry for each device type
                for (const device of devices) {
                    const existingDevice = deviceMap.get(device.device_type);
                    if (!existingDevice || device.updated_at > existingDevice.updated_at) {
                        deviceMap.set(device.device_type, device);
                    }
                }
                // Convert the map values to an array and sort them by `updated_at` in descending order
                return Array.from(deviceMap.values()).sort((a, b) => b.updated_at - a.updated_at);
            }

            const masterSong = await pickMasterSong()
            const notiContent = await pickNotificationContent()
            if (!masterSong || !notiContent) {
                return
            }

            const song_url = await awsS3.getSignedUrl(masterSong.thumbnail_path, 24 * 3600);

            let filter = {}
            const emails = req.body.emails as string[] || []
            if (Array.isArray(emails) && emails.length) {
                filter = { email_id: { $in: emails } }
            }

            const cursor = UserModel.find(filter).cursor()

            for (let user = await cursor.next(); user != null; user = await cursor.next()) {
                console.log(`Processing item:  ${user.email_id}`);

                await pnHelper.sendRichPN({
                    action: {},
                    payload: {
                        title: notiContent.title,
                        body: replaceNotiBody(notiContent.body, masterSong),
                        imageUrl: song_url
                    },
                    token_list: getUniqueDeviceTypesSorted(user.pn_device_list || []),
                    data: {
                        fcm_type: FCMType.song_checkout_cron,
                        master_song_id: masterSong._id.toString(),
                        genre: masterSong.default_genre!,
                        genre_id: masterSong.default_genre_id!,
                        title: masterSong.title,
                        singer: masterSong.singer,
                    }
                })

            }
            // next(SuccessResponse.$200S({ sucess: true }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return
        }
    };

    /**@deprecated */
    async get_feeds_alpha(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_feeds_alpha:"
        try {
            const query = {
                skip: Number(req.query.skip) || 0,
                limit: Number(req.query.limit) || 10,
            }
            const feeds = await CoverFeedModel.find({ feed_type: CoverFeedType.artist_cover }).sort({ created_at: -1 }).skip(query.skip).limit(query.limit)

            await Promise.all(feeds.map(async feed => {
                if (feed.final_video_file_path) {
                    const medialUrl = await awsS3.getSignedUrl(feed.final_video_file_path);
                    feed.final_video_file_path = medialUrl;
                }
                if (feed.thumbnail_path) {
                    const thumbnailUrl = await awsS3.getSignedUrl(feed.thumbnail_path);
                    feed.thumbnail_path = thumbnailUrl;
                }
            }));
            return next(SuccessResponse.$200S({ feeds }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async flush_memory_cache(req: Request, res: Response, next: NextFunction) {
        try {
            MemoryCacheSingleton.clear()
            return next(SuccessResponse.$200S(true))
        } catch (error: any) {
            return next(ErrorResponse.$500E(error))
        }
    };

}

const utilitiesController = new UtilitiesController()
export default utilitiesController