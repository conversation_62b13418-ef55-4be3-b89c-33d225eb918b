import { Request, Response, NextFunction } from 'express'
import { ErrorResponse, SuccessResponse } from '../../utils/Extension'
import Utils from '../../utils/Utils'
import UserModel, { PNDeviceInfo, PNDeviceType } from '../../db/models/UserMaster'
import { Types, } from 'mongoose'
import UserPreferencesModel from '../../db/models/UserPreferences'
import UserRecordingModel, { RecordingListProjectionKey, UserRecordingBean } from '../../db/models/UserRecordings'
import ArtistMasterModel, { ArtistMasterBean } from '../../db/models/ArtistMaster'
import { HomeFeedTilesBean } from '../../db/models/UserHomeFeedTiles'
import SongMasterModel, { SongLanguage, SongMasterBean, SongMasterProjectionKey, SongStatus } from '../../db/models/SongMaster'
import awsS3, { S3BucketName } from '../../utils/StorageHelper'
import { CoverFeedBean } from '../../db/models/CoverFeed'
import { Env } from '../../Env'
import coverFeedDataService from '../../data-services/cover-feed-data.service'
import homeTilesDataService from '../../data-services/home-tiles-data.service'
import songMasterDataService from '../../data-services/song-master-data.service'
import userPreferencesDataService from '../../data-services/user-preferences-data.service'
import homeGridsDataService from '../../data-services/home-grids-data.service'
import artistMasterDataService from '../../data-services/artist-master-data.service'
import userRecordingdDataService from '../../data-services/user-recording-data.service'
import MemoryCacheSingleton from '../../utils/MemoryCache'



class UserHelper {

    static async createUserTilesResponse(tiles: HomeFeedTilesBean[]) {
        try {
            const tile_master_id_map = new Map()
            const all_master_song_map = new Map<string, SongMasterBean>()
            const all_master_song_ids: string[] = []
            const universal_tiles = new Map<string, HomeFeedTilesBean>()
            const user_tiles = new Map<string, HomeFeedTilesBean>()

            // Separate universal and user-specific tiles
            tiles.forEach(tile => {
                if (tile.user_id?.toString() === Env.RECSYS_UNIVERSAL_USER_ID) {
                    universal_tiles.set(tile.tile_name || '', tile)
                } else {
                    user_tiles.set(tile.tile_name || '', tile)
                }
            })

            // Create final tiles list with user tiles taking precedence
            let final_tiles: HomeFeedTilesBean[] = []
            universal_tiles.forEach((universal_tile, tile_name) => {
                const user_tile = user_tiles.get(tile_name)
                final_tiles.push(user_tile || universal_tile)
            })

            // Add remaining user tiles that don't match universal tiles
            user_tiles.forEach((user_tile, tile_name) => {
                if (!universal_tiles.has(tile_name)) {
                    final_tiles.push(user_tile)
                }
            })

            for (let tile of final_tiles) {
                const preview_songs_ids: any[] = []
                for (let i = 0; i < 10; i++) {
                    const song_p_oid = tile.song_id_list_map?.[i]
                    song_p_oid && preview_songs_ids.push(song_p_oid)
                }
                tile_master_id_map.set(tile._id?.toString(), preview_songs_ids)
                all_master_song_ids.push(...preview_songs_ids)
            }

            const unique_master_song_oids = Array.from(new Set(all_master_song_ids)).map(id => new Types.ObjectId(id))
            const master_songs: SongMasterBean[] = await songMasterDataService.getActiveSongsByIds(unique_master_song_oids)

            master_songs.forEach(async (song) => {
                all_master_song_map.set(song._id.toString(), song)
            })

            let gte_three: any[] = []
            let lt_three: any[] = []

            for (let tile of final_tiles) {
                let current_tile: any = {
                    _id: tile._id,
                    song_preview_list: [],
                    tile_name: tile.tile_name,
                    tile_order: tile.tile_order,
                }

                let preview_song_ids: string[] = tile_master_id_map.get(tile._id?.toString()) || []
                preview_song_ids?.forEach((id: string) => {
                    let song = all_master_song_map.get(id)
                    !!song && current_tile.song_preview_list?.push(song)
                })

                if (current_tile.song_preview_list.length >= 3) {
                    gte_three.push(current_tile)
                } else {
                    current_tile.song_preview_list.length && lt_three.push(current_tile)
                }
            }

            gte_three = Utils.shuffleArray(gte_three)
            lt_three = Utils.shuffleArray(lt_three)
            return [...gte_three, ...lt_three]
        } catch (error: any) {
            throw new Error(error.message);
        }
    }
}





class UserController {

    async get_profile(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_user_profile_api:"
        try {

            const query = { user_id: (req as any)._embed._id?.toString() as string }
            const [user, preferences] = await Promise.all([
                UserModel.findOne({ _id: new Types.ObjectId(query.user_id), is_deleted: false }),
                UserPreferencesModel.findOne({ user_id: new Types.ObjectId(query.user_id) })
            ]);
            if (!user) { return next(ErrorResponse.$404("User")) };

            (user as any)["preferences"] = { genres: preferences?.commercial_genre_ids, artists: preferences?.artist_ids }

            return next(SuccessResponse.$200S(user))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async delete_account_soft(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = "delete_account_soft:"
        try {
            const query = { user_id: (req as any)._embed._id?.toString() as string }
            const del = await UserModel.findByIdAndUpdate(
                { _id: new Types.ObjectId(query.user_id) },
                { $set: { is_deleted: true } },
                { returnDocument: "after" }
            )

            return next(SuccessResponse.$200S(del))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async get_user_recordings(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_user_recordings:"
        try {
            const query = {
                sort: req.body.sort,
                skip: req.body.skip || 0,
                limit: req.body.limit || 10,
                search: req.body.search?.toString() as string,
                user_id: (req as any)._embed._id?.toString() as string
            }

            let filters = { user_id: new Types.ObjectId(query.user_id), is_deleted: false }
            const recordings = await userRecordingdDataService.getRecordings({
                filter: filters,
                projection: RecordingListProjectionKey,
                sort: { created_at: -1 }
            })

            await Promise.all(recordings.map(async rec => {
                if (rec.final_mixed_audio_path) {
                    rec.final_mixed_audio_path = await awsS3.getSignedUrl(rec.final_mixed_audio_path);
                }
                if (rec.thumbnail_path) {
                    rec.thumbnail_path = await awsS3.getSignedUrl(rec.thumbnail_path);
                }
            }));

            return next(SuccessResponse.$200S({ recordings }))

        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async save_recording(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":save_recording:"
        try {
            // TODO: update cache
            const params = {
                user_id: (req as any)._embed._id?.toString() as string
            }
            let recording: UserRecordingBean = {
                _id: new Types.ObjectId(),
                user_id: new Types.ObjectId(params.user_id),
                master_song_id: new Types.ObjectId(req.body.master_song_id as string),
                created_at: Date.now(),
                is_deleted: false,
                title: req.body.title,
                vocal_filter_name: req.body.vocal_filter_name,
                raw_audio_file_path: req.body.raw_audio_file_path,
                filtered_vocal_audio_path: req.body.filtered_vocal_audio_path,
                thumbnail_path: req.body.thumbnail_path,
                genre: req.body.genre,
                scale: req.body.scale,
                tempo: req.body.tempo,
                genre_id: req.body.genre_id,
                vocal_volume: req.body.vocal_volume,
                bgm_volume: req.body.bgm_volume,
                latency: req.body.latency,
                denoise: req.body.denoise,
                input_mic: req.body.input_mic || undefined,
                device: req.body.device,
                os: req.body.os,
                final_mixed_audio_path: req.body.final_mixed_audio_path,
                master_filter_name: req.body.master_filter_name,
            }

            // for (let key in recording) {
            //     const typedKey = key as keyof UserRecordingBean;
            //     if (typeof recording[typedKey] === "string" && recording[typedKey] === "") {
            //         return next(ErrorResponse.$400(`Empty string is not allowed in: ${key}`))
            //     }
            // }

            const rawVocalKey = recording.raw_audio_file_path?.replace(`/${S3BucketName.ttl_24hrs}/`, "");
            await awsS3.copyObject(S3BucketName.ttl_24hrs, rawVocalKey, S3BucketName.final_recording, rawVocalKey)
            recording.raw_audio_file_path = `/${S3BucketName.final_recording}/${rawVocalKey}`
            if (!recording.thumbnail_path) {
                const song = await SongMasterModel.findById(recording.master_song_id);
                if (song && song.thumbnail_path) {
                    recording.thumbnail_path = song.thumbnail_path
                }
            }
            Utils.removeUndefinedProps(recording);

            const inserted_recording = await UserRecordingModel.create(recording)
            // TODO: Temporary Fix for cache refresh
            MemoryCacheSingleton.clear()
            return next(SuccessResponse.$201S(inserted_recording))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500E(error.message))
        }
    }

    async recording_save_as_final(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":recording_save_as_final:"
        try {
            // TODO: update cache
            const query = {
                _id: req.body.recording_id as string,
                user_id: (req as any)._embed._id?.toString() as string
            }
            const recording = await UserRecordingModel.findByIdAndUpdate(
                { _id: new Types.ObjectId(query._id), user_id: new Types.ObjectId(query.user_id), },
                [{
                    $set: {
                        is_final_save: {
                            $cond: {
                                if: { $eq: ["$is_final_save", true] },
                                then: false,
                                else: true
                            }
                        }
                    }
                }],
                { returnDocument: "after" }
            )
            // TODO: Temporary Fix for cache refresh
            MemoryCacheSingleton.clear()
            return next(SuccessResponse.$200S(recording))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async delete_recording(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":delete_user_recording:"
        try {
            // TODO: update cache
            const recording_id = req.body.recording_id?.toString() as string
            const deleted = await UserRecordingModel.updateOne({ _id: new Types.ObjectId(recording_id) }, { $set: { is_deleted: true } })
            // TODO: Temporary Fix for cache refresh
            MemoryCacheSingleton.clear()
            return next(SuccessResponse.$200S({ deleted }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    }

    async list_preferences(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_;
        const flag = ":list_preferences:";
        try {
            const query = {
                search_text: req.query.search_text as string,
                skip: Number(req.query.skip) || 0,
                limit: Number(req.query.limit) || 100
            };

            const filters: any = { is_active: true };
            let artists: ArtistMasterBean[] = [];
            let isSearchFilter = false;

            if (query.search_text) {
                const escapedSearch = Utils.escapeChar(query.search_text).trim();
                const startsWithRegex = `^${escapedSearch}`;

                artists = await ArtistMasterModel.aggregate([
                    {
                        $match: {
                            is_active: true,
                            artist_name: { $regex: escapedSearch, $options: "i" },
                        },
                    },
                    {
                        $addFields: {
                            match_priority: {
                                $cond: {
                                    if: {
                                        $regexMatch: {
                                            input: "$artist_name",
                                            regex: startsWithRegex,
                                            options: "i",
                                        },
                                    },
                                    then: 0,
                                    else: 1,
                                },
                            },
                        },
                    },
                    { $sort: { match_priority: 1, artist_name: 1 } },
                    { $project: { match_priority: 0 } },
                    { $skip: query.skip },
                    { $limit: query.limit }
                ]);
                isSearchFilter = true;
            } else {
                artists = await artistMasterDataService.getArtists({
                    filter: filters,
                    skip: query.skip,
                    limit: query.limit
                })
            }

            artists = isSearchFilter ? artists : Utils.shuffleArray(artists);

            await Promise.all(
                artists.map(async (artist) => {
                    if (artist.thumbnail_path) {
                        let url = await awsS3.getSignedUrl(artist.thumbnail_path);
                        artist.thumbnail_path = url;
                    }
                })
            );

            const pagination = Utils.getPagination({
                skip: query.skip,
                limit: query.limit,
                docLength: artists.length
            })
            return next(SuccessResponse.$200S(artists, pagination));
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500);
        }
    }

    async save_preferences(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":save_user_preferences:"
        try {
            // TODO: update cache
            const params = {
                user_id: (req as any)._embed._id?.toString() as string,
                commercial_genre_ids: req.body.commercial_genre_ids as string[] || [],
                artist_ids: req.body.arist_ids as string[] || []
            }

            const preferences = await UserPreferencesModel.findOneAndUpdate(
                { user_id: new Types.ObjectId(params.user_id) },
                {
                    $set: {
                        commercial_genre_ids: params.commercial_genre_ids,
                        artist_ids: params.artist_ids
                    }
                },
                { upsert: true, new: true }
            )

            // TODO: Temporary Fix for cache refresh
            MemoryCacheSingleton.clear()
            return next(SuccessResponse.$200S({ preferences }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async denoise_raw_vocal(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":denoise_raw_vocal:"
        try {
            const params = {
                user_id: (req as any)._embed._id,
                filename: req.body.filename as string,
            }
            const vocal_s3_path = `${S3BucketName.ttl_24hrs}/raw_vocal_audio/${params.user_id}/${params.filename}`
            const raw_vocal_get_signed_url = await awsS3.getSignedUrl(vocal_s3_path)
            return next(SuccessResponse.$200S({
                denoised_raw_vocal_url: raw_vocal_get_signed_url,
                path: vocal_s3_path,
            }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async home_feed_tiles(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":home_feed_tiles:"
        try {
            const user_id = (req as any)._embed._id?.toString() as string
            let tiles = await homeTilesDataService.getTiles({
                filter: { user_id: { $in: [new Types.ObjectId(Env.RECSYS_UNIVERSAL_USER_ID), new Types.ObjectId(user_id)] } },
                sort: { tile_order: 1 }
            })

            const new_tiles = await UserHelper.createUserTilesResponse(tiles)

            return next(SuccessResponse.$200S({ tiles: new_tiles }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async home_feed_tiles_populate_songs(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ':home_feed_tiles_populate_songs:'
        try {
            const params = {
                // user_id: Env.RECSYS_UNIVERSAL_USER_ID,
                skip: Number(req.body.skip) || 0,
                limit: 300,
                tile_id: new Types.ObjectId(req.body.tile_id as string),
            }

            const user_tile = await homeTilesDataService.findById(params.tile_id)
            if (!user_tile) return next(ErrorResponse.$404("Tile"))

            const tile_song_ids: Types.ObjectId[] = []
            for (let start = params.skip; start < (params.skip + params.limit); start++) {
                const id = user_tile!.song_id_list_map![`${start}`]
                if (id) {
                    tile_song_ids.push(new Types.ObjectId(id))
                }
            }

            const tile_songs = await songMasterDataService.getActiveSongsByIds(tile_song_ids)
            let you_may_like_songs: SongMasterBean[] = []

            if (tile_song_ids.length < params.limit) {
                you_may_like_songs = await songMasterDataService.getSongs({
                    filter: {
                        _id: { $nin: tile_song_ids },
                        song_status: SongStatus.active
                    },
                    projection: SongMasterProjectionKey,
                })
            }

            await Promise.all(
                you_may_like_songs.map(async (song) => {
                    if (song.thumbnail_path) {
                        song.thumbnail_path = await awsS3.getSignedUrl(song.thumbnail_path);
                    }
                })
            );

            const page = Utils.getPagination({
                skip: params.skip,
                limit: params.limit,
                docLength: (tile_songs.length + you_may_like_songs.length)
            })
            return next(SuccessResponse.$200S({ tile_songs, you_may_like_songs }, page))

        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async home_feed_grids(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":home_feed_grids:"
        try {
            const params = {
                user_id: (req as any)._embed._id?.toString() as string,
            }

            const preferences = await userPreferencesDataService.getPreferences(new Types.ObjectId(params.user_id))
            const prefArtistIds = preferences?.artist_ids?.map(id => new Types.ObjectId(id)) || []

            const [feed_grids, artists] = await Promise.all([
                homeGridsDataService.getGridsByUserId(prefArtistIds),
                artistMasterDataService.getArtistByIds(prefArtistIds)
            ])

            let new_grids: any[] = []
            feed_grids.forEach((grid) => {
                let current_grid: any = {
                    _id: grid._id,
                    grid_order: grid.grid_order,
                    grid_type: grid.grid_type,
                }

                if (grid.grid_type === "artist") {
                    const artistMaster = artists.find((artist: ArtistMasterBean) => artist._id?.toString() === grid.artist_id?.toString()) as ArtistMasterBean | undefined
                    current_grid["artist_name"] = artistMaster?.artist_name;
                    current_grid["grid_name"] = artistMaster?.artist_name;
                    current_grid["thumbnail_path"] = artistMaster?.thumbnail_path;
                }
                new_grids.push(current_grid)
            })

            return next(SuccessResponse.$200S(new_grids))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async home_feed_grids_populate_songs(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":home_feed_grids_populate_songs_v2:"
        try {
            const params = {
                user_id: Env.RECSYS_UNIVERSAL_USER_ID,
                skip: Number(req.body.skip) || 0,
                // limit: Number(req.body.limit) || 100,
                limit: 300,
                grid_id: req.body.grid_id as string,
            }

            const user_grid = await homeGridsDataService.findById(new Types.ObjectId(params.grid_id))
            if (!user_grid) return next(ErrorResponse.$404("Grid"))
            const grid_song_ids: Types.ObjectId[] = []

            for (let start = params.skip; start < (params.skip + params.limit); start++) {
                const id = user_grid!.song_id_list_map![`${start}`]
                if (id) {
                    grid_song_ids.push(new Types.ObjectId(id))
                }
            }

            // let grid_songs: SongMasterBean[] = []
            let you_may_like_songs: SongMasterBean[] = []
            const grid_songs = await songMasterDataService.getActiveSongsByIds(grid_song_ids)

            if (grid_song_ids.length < params.limit) {
                you_may_like_songs = await songMasterDataService.getSongs({
                    filter: {
                        _id: { $nin: grid_song_ids },
                        song_status: SongStatus.active
                    },
                    projection: SongMasterProjectionKey
                })
            }
            // TODO: Need to implement a common function for this
            await Promise.all(
                you_may_like_songs.map(async (song) => {
                    if (song.thumbnail_path) {
                        let url = await awsS3.getSignedUrl(song.thumbnail_path);
                        song.thumbnail_path = url;
                    }
                })
            );

            const page = Utils.getPagination({
                skip: params.skip,
                limit: params.limit,
                docLength: (grid_songs.length + you_may_like_songs.length)
            })
            return next(SuccessResponse.$200S({ grid_songs, you_may_like_songs }, page))

        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async update_fcm_devices(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":update_pn_devices:"
        next(SuccessResponse.$200S({ success: true }))
        try {
            // Validate required fields early
            const { device_id, device_type, fcm_token } = req.body;
            if (!device_id || !device_type || !fcm_token) {
                console.error(_rid, flag, "Missing required fields");
                return;
            }

            const params = {
                user_id: (req as any)._embed._id?.toString() as string,
                device_info: {
                    device_id,
                    device_type,
                    fcm_token,
                    updated_at: Date.now()
                } as PNDeviceInfo
            }

            const user = await UserModel.findById(new Types.ObjectId(params.user_id))
            if (!user) {
                console.error(_rid, flag, params.user_id, "USER_NOT_FOUND")
                return
            }

            if (!user.pn_device_list) {
                user.pn_device_list = []
            }
            // if device_id is already present, update the device info
            let device_index = user.pn_device_list?.findIndex(d => d.device_id === params.device_info.device_id)
            if (typeof device_index === "number" && device_index > -1) {
                user.pn_device_list[device_index] = params.device_info
            } else {
                user.pn_device_list?.push(params.device_info)
            }

            const cutoffTimestamp = Date.now() - 30 * 24 * 60 * 60 * 1000; // 30 days ago
            const deviceTypeMap = new Map<PNDeviceType, PNDeviceInfo[]>();

            // This combines finding the device, filtering old devices, and grouping by type
            for (const device of user.pn_device_list) {
                if (device.updated_at < cutoffTimestamp) continue;

                // Group by device type
                const devices = deviceTypeMap.get(device.device_type) || [];
                devices.push(device);
                deviceTypeMap.set(device.device_type, devices);
            }

            const newDeviceList: PNDeviceInfo[] = [];
            for (const [_, devices] of deviceTypeMap) {
                // Sort and slice in one operation per device type
                newDeviceList.push(
                    ...devices
                        .sort((a, b) => b.updated_at - a.updated_at)
                        .slice(0, 3)
                );
            }

            // Update the user's device list
            user.pn_device_list = newDeviceList;
            await user.save();
            return;

        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return;
        }
    };

    async get_feeds_by_sources(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_feeds_by_source:"
        try {
            const params = {
                limit: 300,
                source: {
                    page_name: req.body.page_name as "personalisation" | "search" | "home",
                    clicked_source: req.body.clicked_source as "listen" | "thumbnail" | "tile",
                },
                data: {
                    master_song_id: req.body.master_song_id as string,
                    genre: req.body.genre as string,
                    genre_id: req.body.genre_id as string,
                    lang: req.body.lang as SongLanguage
                }
            }

            const feeds = await coverFeedDataService.getAllCoverFeeds()
            const feeds_weight_map: Map<number, CoverFeedBean[]> = new Map()

            let no_content = true

            feeds.map(async feed => {
                if (params.data.master_song_id && params.data.genre && params.data.genre_id) {
                    if (feed.master_song_id?.toString() === params.data.master_song_id && feed.genre === params.data.genre && feed.genre_id === params.data.genre_id) {
                        feeds_weight_map.set(4, [...(feeds_weight_map.get(4) || []), feed]);
                        no_content = false
                    } else if (feed.master_song_id?.toString() === params.data.master_song_id) {
                        feeds_weight_map.set(3, [...(feeds_weight_map.get(3) || []), feed]);
                    } else {
                        feeds_weight_map.set(1, [...(feeds_weight_map.get(1) || []), feed]);
                    }
                } else if (params.data.master_song_id && params.data.genre) {
                    if (feed.master_song_id?.toString() === params.data.master_song_id && feed.genre === params.data.genre) {
                        feeds_weight_map.set(4, [...(feeds_weight_map.get(4) || []), feed]);
                        no_content = false
                    } else if (feed.genre === params.data.genre) {
                        feeds_weight_map.set(3, [...(feeds_weight_map.get(3) || []), feed]);
                    } else if (feed.master_song_id?.toString() === params.data.master_song_id) {
                        feeds_weight_map.set(2, [...(feeds_weight_map.get(2) || []), feed]);
                    } else {
                        feeds_weight_map.set(1, [...(feeds_weight_map.get(1) || []), feed]);
                    }
                } else if (params.data.master_song_id) {
                    if (feed.master_song_id?.toString() === params.data.master_song_id) {
                        feeds_weight_map.set(2, [...(feeds_weight_map.get(4) || []), feed]);
                        no_content = false
                    } else {
                        feeds_weight_map.set(1, [...(feeds_weight_map.get(1) || []), feed]);
                    }
                } else if (params.data.genre) {
                    if (feed.genre === params.data.genre) {
                        feeds_weight_map.set(2, [...(feeds_weight_map.get(2) || []), feed]);
                        no_content = false
                    } else {
                        feeds_weight_map.set(1, [...(feeds_weight_map.get(1) || []), feed]);
                    }
                } else {
                    feeds_weight_map.set(1, [...(feeds_weight_map.get(1) || []), feed]);
                    no_content = false
                }
            });

            if (no_content) {
                return next(SuccessResponse.$200S({ feeds: [], no_content }))
            }

            let sorted_feeds: CoverFeedBean[] = []
            let sorted_weight = [...feeds_weight_map.keys()].sort((a, b) => b - a)
            sorted_weight.forEach(weight => {
                sorted_feeds.push(...feeds_weight_map.get(weight) || [])
            })

            if (params.data.lang !== SongLanguage.HINDI) {
                sorted_feeds = sorted_feeds.filter(feed => feed.lang !== SongLanguage.HINDI)
            }

            return next(SuccessResponse.$200S({ feeds: sorted_feeds }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };
}

const userController = new UserController()
export default userController
