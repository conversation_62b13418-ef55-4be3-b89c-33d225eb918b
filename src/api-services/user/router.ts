import { Router } from 'express'
import userController from './controller'

/**
 * '/user'
 */
const user_router = Router()


user_router.get('/v1/get_profile', userController.get_profile)
user_router.put('/v1/fcm_devices', userController.update_fcm_devices)
user_router.delete('/v1/delete', userController.delete_account_soft)
user_router.get('/v2/preferences', userController.list_preferences)
user_router.post('/v1/save_preferences', userController.save_preferences)

user_router.post('/v2/home_feed_tiles', userController.home_feed_tiles)
user_router.post('/v2/home_feed_tiles/populate_songs', userController.home_feed_tiles_populate_songs)

user_router.post('/v2/home_feed_grids', userController.home_feed_grids)
user_router.post('/v2/home_feed_grids/populate_songs', userController.home_feed_grids_populate_songs)

user_router.post('/v2/denoise_raw_vocal', userController.denoise_raw_vocal)
user_router.post('/v2/get_recordings', userController.get_user_recordings)
user_router.post('/v3/save_recording', userController.save_recording)
user_router.patch('/v1/save_as_final', userController.recording_save_as_final)
user_router.post('/v1/delete_recording', userController.delete_recording)

user_router.post('/v2/feeds', userController.get_feeds_by_sources)


export default user_router