import { WebSignupFromBean } from "../../db/models/MainWebsite"



export function getTimestamp() {
    return new Date().toLocaleString('en-GB', { timeZone: 'Asia/Kolkata', hour12: false })
}




export function newSignupNotiEmail(args: WebSignupFromBean) {
    return `<p style="text-align:center; color:#00A85F; font-size: 32px;"><strong>User Details</strong> </p>
<p style="text-align:center">User Name: <span style="font-size:22px"><strong>${args.first_name}</strong></span></p>
<p style="text-align:center">Email: <span style="font-size:22px"><strong>${args.email}</strong></span></p>
<p style="text-align:center">Phone: <span style="font-size:22px"><strong>${args.phone || "NA"}</strong></span></p>
<p style="text-align:center">Help: <span style="font-size:22px"><strong>${args.help_asked}</strong></span></p>
<p style="text-align:center">Find Via: <span style="font-size:22px"><strong>${args.find_us_via || "NA"}</strong></span></p>
<p style="text-align:center">Sample Song: <span style="font-size:22px"><strong>${args.shared_audio_url || args.uploaded_audio_path}</strong></span></p>
`
}