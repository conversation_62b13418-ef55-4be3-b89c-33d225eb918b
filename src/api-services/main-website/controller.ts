import { NextFunction, Request, Response } from 'express'
import { Types } from 'mongoose'
import { ErrorResponse, SuccessResponse } from '../../utils/Extension'
import WebSignupFromModel, { WebSignupFromBean } from '../../db/models/MainWebsite'
import awsS3, { S3BucketName } from '../../utils/StorageHelper'
import discordWrapper from '../../utils/DiscordWrapper'
import { EmailBody } from '../../constants/EmailBody'
import { AdminEmail, MailSenderAddress, SESMailer } from '../../utils/SESMailer'


class MainWebsiteController {

    async submit_form_api(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":submit_form_api:"
        try {
            const form_req: WebSignupFromBean = {
                _id: new Types.ObjectId(),
                first_name: req.body.fname || "Folk",
                // last_name: req.body.lname,
                email: req.body.email,
                phone: req.body.phone,
                help_asked: req.body.musichelp,
                find_us_via: req.body.findUs,
                social_media_link: req.body.socialMediaLink,
                created_at: Date.now()
            }

            const form = await WebSignupFromModel.create(form_req)

            SESMailer.getInstance().fromMrMelo({
                to: form.email,
                subject: "🎶 Welcome to Melodyze - You're In!",
                body: EmailBody.webSignupWelcome(form.first_name.split(" ")[0])
            }).then()

            SESMailer.getInstance().fromMrMelo({
                to: MailSenderAddress.MR_MELO,
                subject: "[Important] New Signup Melodyze.ai Web",
                body: EmailBody.webSignupAdminNotification(form)
            }).then()

            discordWrapper.new_web_form_signup(form).then()

            return next(SuccessResponse.$200S(form))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            if (error.code === 11000 && error.keyPattern?.email) {
                return next(ErrorResponse.$400("Email is already registered."))
            }
            return next(ErrorResponse.$500)
        }
    };
}

const mainWebsiteController = new MainWebsiteController()
export default mainWebsiteController





// export const get_signed_url_api = async (req: Request, res: Response) => {
//     try {
//         console.log(`${getTimestamp()} : ⬇ : ${req.method}~${req.path} : ${JSON.stringify(req.body)}`)
//         let file_ext = req.query.ext
//         let key = `uploaded_audio/${Date.now()}.${file_ext}`
//         let path = `/signup-forms/${key}`
//         let upload_url = await generatePutSignedUrl(key)

//         console.log(`${getTimestamp()} : 🟩 :  ${req.method}~${req.path} : success`)
//         return res.status(200).send({
//             success: true,
//             data: {
//                 upload: {
//                     url: upload_url,
//                     path: path
//                 }
//             }
//         })
//     } catch (error: any) {
//         console.error(`${getTimestamp()} : 🟥 : ${req.method}~${req.path} : failed`)
//         return res.status(500).send({ success: false, error: { message: error.message } })
//     }
// };


// export const get_analytics_api = async (req: Request, res: Response) => {
//     try {
//         const total = await getTotalItemCount()
//         const today = await getTodaysSignup()
//         res.send(`
//     <!DOCTYPE html>
//     <html>
//     <head>
//       <title>Melodyze.ai</title>
//     </head>
//     <body>
//       <h1 style="text-align:center;">Signup Analytics</h1></br>
//       <p style="text-align:center; font-size:22px"><strong>Total: </strong>${total}</p>
//       <p style="text-align:center; font-size:22px"><strong>Today: </strong>${today}<p/>
//     </body>
//     </html>
//   `)
//     } catch (error: any) {
//         console.error(`${getTimestamp()} : 🟥 : ${req.method}~${req.path} : failed`)
//         return res.status(500).send({ success: false, error: { message: error.message } })
//     }
// };


// export const check_email_exists_api = async (req: Request, res: Response) => {
//     try {
//         const exists = await isEmailExists(req.body.email)
//         return res.status(200).send({ success: true, exists })
//     } catch (error: any) {
//         console.error(`${getTimestamp()} : 🟥 : ${req.method}~${req.path} : ${error.message}`)
//         return res.status(500).send({ success: false, error: { message: error.message } })
//     }
// }