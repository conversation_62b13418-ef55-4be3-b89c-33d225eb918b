import { Request, Response, NextFunction } from 'express'
import { ErrorResponse, SuccessResponse } from '../../utils/Extension'
import Utils from '../../utils/Utils'
import SongMasterModel, { SongMasterProjectionKey, SongStatus } from '../../db/models/SongMaster'
import AnnotatedSongModel from '../../db/models/AnnotatedSongs'
import { Types, } from 'mongoose'
import awsS3, { S3BucketName } from '../../utils/StorageHelper'
import { WavTempoChange } from '../../utils/waveTempoChange'
import annotatedSongDataService from '../../data-services/annotated-song-data.service'
import songMasterDataService from '../../data-services/song-master-data.service'


class SongController {

    async search_songs(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_;
        const flag = ":search_songs_v2:";
        try {
            const query = {
                skip: Number(req.body.skip) || 0,
                limit: 300,
                search_text: req.body.search_text?.toString(),
            };

            if (!query.search_text) return next(ErrorResponse.$400("Error: search_text is required."));

            query.search_text = Utils.normalizeSongTitle(query.search_text).trim();
            const escapedSearch = Utils.escapeChar(query.search_text);
            const startsWithRegex = `^${escapedSearch}`;

            const [matched_songs, you_may_like_songs] = await Promise.all([
                SongMasterModel.aggregate([
                    {
                        $match: {
                            song_status: SongStatus.active,
                            normalized_title: { $regex: escapedSearch, $options: "i" },
                        },
                    },
                    {
                        $addFields: {
                            match_priority: {
                                $cond: {
                                    if: {
                                        $regexMatch: { input: "$normalized_title", regex: startsWithRegex, options: "i" },
                                    },
                                    then: 0,
                                    else: 1,
                                },
                            },
                        },
                    },
                    { $sort: { match_priority: 1, normalized_title: 1 } },
                    { $project: SongMasterProjectionKey },
                    // { $skip: query.skip },
                    { $limit: query.limit },
                ]),
                SongMasterModel.aggregate([
                    {
                        $match: {
                            song_status: SongStatus.active,
                            normalized_title: { $not: { $regex: escapedSearch, $options: "i" } }
                        },
                    },
                    { $sample: { size: query.limit } },
                    { $project: SongMasterProjectionKey },
                ])
            ])

            await Promise.all(
                [...matched_songs, ...you_may_like_songs].map(async (song) => {
                    if (song.thumbnail_path) {
                        song.thumbnail_path = await awsS3.getSignedUrl(song.thumbnail_path);
                    }
                })
            );

            const page = Utils.getPagination({
                skip: query.skip,
                limit: query.limit,
                docLength: (matched_songs.length + you_may_like_songs.length),
            })
            return next(SuccessResponse.$200S({ matched_songs, you_may_like_songs }, page))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500);
        }
    }

    async get_master_song_by_id(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_master_song_by_id:"
        try {
            const id = req.params.id as string
            const song = (await songMasterDataService.getActiveSongById(new Types.ObjectId(id)))
            if (!song) return next(ErrorResponse.$400("Song not found."))
            return next(SuccessResponse.$200S(song))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };

    async get_annotated_songs(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_annotated_songs:"
        try {
            const params = {
                master_song_id: req.body.master_song_id?.toString() as string,
            }
            let list = await annotatedSongDataService.getAllAnnotatedSongsById(params.master_song_id)
            return next(SuccessResponse.$200S(list))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    }

    async get_tempo_changed_wav_path(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":get_tempo_changed_wav_path:"
        try {
            const query = {
                master_song_id: new Types.ObjectId(req.body.master_song_id as string),
                genre: req.body.genre,
                genre_id: req.body.genre_id,
                scale: req.body.scale,
            }
            const user_tempo = req.body.user_tempo?.toString()

            const wav_path_annotated_song = await AnnotatedSongModel.findOne({
                master_song_id: query.master_song_id,
                genre: query.genre,
                genre_id: query.genre_id,
                scale: query.scale,
                wav_song_path: { $exists: true }
            }, { wav_song_path: 1, tempo: 1 })

            if (!wav_path_annotated_song) {
                return next(ErrorResponse.$400("No wav audio file found for this song."))
            }
            const respObj = {
                wav_song_path: "",
                tempo: user_tempo,
                master_song_id: query.master_song_id,
                genre: query.genre,
                genre_id: query.genre_id,
                scale: query.scale,
            }

            if (user_tempo === wav_path_annotated_song?.tempo) {
                respObj.wav_song_path = await awsS3.getSignedUrl(wav_path_annotated_song.wav_song_path!)
                return next(SuccessResponse.$200S(respObj))

            } else {
                let inputKey = wav_path_annotated_song.wav_song_path?.replace(`/${S3BucketName.annotated_song_wav}/`, "")
                let outputKey = `${query.master_song_id}/${query.genre}_${query.genre_id}/${user_tempo}_${query.scale}.wav`

                if (await awsS3.isObjectExists(S3BucketName.annotated_song_wav_cache, outputKey)) {
                    console.log("isObjectExists: true", outputKey)

                } else {
                    console.log("changingTempo:", inputKey, wav_path_annotated_song?.tempo, "to:", user_tempo)
                    const tempoChange = await WavTempoChange.handler(inputKey!, outputKey, Number(wav_path_annotated_song.tempo), Number(user_tempo))
                    console.log("tempoChange.outputFileKey:", tempoChange.outputFileKey)
                }

                const new_wav_path = `/${S3BucketName.annotated_song_wav_cache}/${outputKey}`
                respObj.wav_song_path = await awsS3.getSignedUrl(new_wav_path)
                return next(SuccessResponse.$200S(respObj))
            }

        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    }
}

const songController = new SongController()
export default songController