import { Router } from 'express'
import songController from './controller'

/**
 * '/song'
 */

const song_router = Router()

song_router.post('/v2/search', songController.search_songs)
song_router.get('/v2/master/:id', songController.get_master_song_by_id)
song_router.post('/v2/annotated/get_all', songController.get_annotated_songs)
song_router.post('/v2/annotated/tempo_changed_wav_path', songController.get_tempo_changed_wav_path)

export default song_router
