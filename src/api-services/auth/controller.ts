import { Request, Response, NextFunction } from 'express'
import { ErrorResponse, SuccessResponse } from '../../utils/Extension'
import Utils from '../../utils/Utils'
import { OAuth2Client } from 'google-auth-library';
import jwt from 'jsonwebtoken'
import admin from "../..";
import { Env } from '../../Env';
import { MeloJwtPayload } from '../../models/Interface';
import UserModel from '../../db/models/UserMaster';
import { Types } from "mongoose"
import MemoryCacheSingleton from '../../utils/MemoryCache';



class AuthHelper {

    readonly oAuth2Client = new OAuth2Client({
        clientId: Env.oauth2_client_id,
        clientSecret: Env.oauth2_client_secret,
        redirectUri: ""
    })


    async validateGoogleAuthToken(token: string) {
        try {
            const ticket = await this.oAuth2Client.verifyIdToken({ idToken: token })
            const payload = ticket.getPayload();
            return { email: payload?.email, name: payload?.name || "Unnamed" };
        } catch (error: any) {
            return { error: error.message }
        }
    }


    async validateFirebaseIdToken(token: string) {
        try {
            const payload = await admin.auth().verifyIdToken(token)
            return { email: payload?.email, name: payload?.name || "Unnamed", uid: payload.uid };
        } catch (error: any) {
            return { error: error.message }
        }
    }

    validateTestToken(token: string) {
        try {
            const payload = jwt.verify(token, Env.jwt_signature) as MeloJwtPayload
            return { email: payload?.email, name: payload?.name || "", test: true };
        } catch (error: any) {
            return { error: error.message }
        }
    }


    generateAuthToken(args: MeloJwtPayload) {
        const payload = {
            _id: args._id,
            email: args.email,
            name: args.name
        }
        return jwt.sign(payload, Env.jwt_signature, {
            expiresIn: '12weeks'
        });
    }

}


export const authHelper = new AuthHelper()


class AuthController {


    async login(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":user_login_api:"
        try {

            const google_auth_token = req.body['x-google-oauth-token'] as string
            const firebase_id_token = req.body['x-firebase-id-token'] as string
            const test_id_token = req.body['x-test-id-token'] as string

            let info
            if (google_auth_token) {
                info = await authHelper.validateGoogleAuthToken(google_auth_token);
            } else if (firebase_id_token) {
                info = await authHelper.validateFirebaseIdToken(firebase_id_token)
            } else if (test_id_token) {
                info = authHelper.validateTestToken(test_id_token)
            } else {
                return next(ErrorResponse.$401('404'))
            }

            if (!info.email) {
                console.error(_rid, flag, info.error)
                return next(ErrorResponse.$401('500'))
            }

            let user = await UserModel.findOne({ email_id: info.email, is_deleted: false })

            let is_reg = false
            if (!user) {
                let user_data = { _id: new Types.ObjectId(), email_id: info.email, user_name: info.name, created_at: Date.now(), is_deleted: false }
                Utils.removeUndefinedProps(user)
                user = await UserModel.create(user_data)

                if (!user) {
                    return next(ErrorResponse.$500C("Error: Registering new user."))
                }
                is_reg = true
                console.info(_rid, "new_user_registration:", user.email_id)
            }

            const access_token = authHelper.generateAuthToken({ _id: user._id?.toString(), email: user.email_id, name: user.user_name })
            res.setHeader('x-access-token', access_token)

            if (is_reg) return next(SuccessResponse.$201C("registration success", { user }));
            // TODO: Need to  remove this cache flush
            MemoryCacheSingleton.clear()
            return next(SuccessResponse.$200("login success", { user }));
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500)
        }
    };


    async logout(req: Request, res: Response, next: NextFunction) {
        const _rid = (req as any)._id_
        const flag = ":logout:"
        try {

            //TODO: Delete FCM token pending

            return next(SuccessResponse.$200S({ success: true }))
        } catch (error: any) {
            console.error(_rid, flag, error.message);
            return next(ErrorResponse.$500E(error))
        }
    };

}

const authController = new AuthController()
export default authController