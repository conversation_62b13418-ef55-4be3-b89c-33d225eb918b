import { Response, Request, NextFunction } from "express"
import jwt, { JwtPayload } from 'jsonwebtoken'
import { ErrorResponse } from "../utils/Extension";
import { Env } from "../Env";
import { MeloJwtPayload } from "../models/Interface";
import { ErrName } from "../Dict";
import { authHelper } from "../api-services/auth/controller";

export const auth_validation = async (req: Request, res: Response, next: NextFunction) => {

    const headers = {
        authorization: req.headers['x-auth-token']?.toString(),
    }
    if (!headers.authorization) {
        return next(ErrorResponse.$401('404'))
    }

    try {
        let decoded = jwt.verify(headers.authorization, Env.jwt_signature) as MeloJwtPayload
        if (!decoded._id || !decoded.email) {
            return next(ErrorResponse.$401("411"))
        }
        (req as any)._embed = decoded

        // issue new token if expiry is less than 4 weeks
        const expiry = decoded.exp || 0
        const now = Math.floor(Date.now() / 1000)
        const four_weeks_in_secs = 3600 * 24 * 7 * 4
        if ((expiry - now) < four_weeks_in_secs) {
            const new_token = authHelper.generateAuthToken(decoded)
            res.setHeader('x-access-token', new_token)
        }

    } catch (error: any) {
        if (error.name === ErrName.JWT_EXPIRED) {
            return next(ErrorResponse.$401("409"));
        }
        return next(ErrorResponse.$401("408"));
    }
    return next();
}