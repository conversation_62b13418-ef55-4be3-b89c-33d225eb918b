import { Request, Response, NextFunction } from "express";
import Util from "../utils/Utils";
import { ErrorResponse, SuccessResponse } from "../utils/Extension";
import { HTTPCode } from "../Dict";


export const pre_logger = async (req: Request, res: Response, next: NextFunction) => {
    const _now = Date.now();
    const reqId = Util.apiReqId;
    console.log(`⬇ : ${req.method}~${req.path} : ${reqId} ▪︎ body: ${JSON.stringify(req.body)} ▪︎ query: ${JSON.stringify(req.query)}`);
    (req as any)._now = _now;
    (req as any)._id_ = reqId;
    next()
}

export const post_logger_final_response = async (resObj: any, req: Request, res: Response, next: NextFunction) => {
    const _now: number = (req as any)._now,
        reqId: string = (req as any)._id_,
        took: number = (Date.now() - _now) / 1000.0,
        path: string = req.path,
        method: string = req.method,
        message: string = resObj.message || 'success';

    let status: number = resObj.status;
    res.setHeader("x-melodyze-request-id", reqId)

    if (resObj instanceof ErrorResponse) {
        let error_msg: string = resObj.message || 'Unknown';
        const result = {
            success: false,
            error: error_msg,
            error_detail: {
                detail: resObj.detail,
                message: error_msg,
                code: resObj.code,
            }
        };
        console.error(`⛔️: ${method}~${path} : ${reqId} : ${took} : ${status} ▪︎ ${JSON.stringify(result)}`);
        res.status(status).send(result);

    } else if (resObj instanceof SuccessResponse) {
        const data = resObj.data;
        const pagination = resObj.pagination
        // const cursor = resObj.cursor

        console.log(`🟢: ${method}~${path} : ${reqId} : ${took} : ${status}`);
        res.status(status).send({ success: true, message, data, pagination });

    } else {
        status = HTTPCode.$200;
        console.log(`🟢: ${method}~${path} : ${reqId} : ${took} : ${status}`);
        res.status(status).send({ success: true, message: 'success', data: resObj, pagination: resObj.pagination, });
    }
}


export const path_not_found = async (req: Request, res: Response, next: NextFunction) => {
    return next(ErrorResponse.path_not_found)
}

export function respLog(req: Request) {
    let reqId: string = (req as any)._id_;
    console.log(`🟢 : ${req.method}~${req.path} : ${reqId} :  : 200`);
}