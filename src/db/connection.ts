// import { Env } from '../Env';
// import crypto from 'crypto'
// import * as AWS_DDB from '@aws-sdk/client-dynamodb'
// import { marshall, unmarshall } from '@aws-sdk/util-dynamodb'
// import { AnnotatedSongBean, AnnotatedSongZipBean, ArtistMasterBean, CommercialGenreMasterBean, SongMasterBean, UserBean, HomeFeedGridsBean, HomeFeedTilesBean, UserPreferencesBean, UserRecordingBean } from './Interface';
// import { HTTPCode, ProjectionKey } from '../Dict';
// import Util from '../utils/Util';
// import { KeyCondExpr } from '../models/Interface';


// export const dynamoClient = new AWS_DDB.DynamoDBClient(
//     {
//         region: Env.aws_ddb_default_region,
//         credentials: {
//             accessKeyId: Env.aws_ddb_access_key_id,
//             secretAccessKey: Env.aws_ddb_secret_access_key,
//         }
//     }
// )

// interface DynamoDBCommandModel<T> {
//     getItem(query: T): Promise<any>;
// }

// class DynamoDBCommand<T> implements DynamoDBCommandModel<T> {
//     tableName: string;

//     constructor(tableName: string) {
//         this.tableName = tableName;
//     }

//     /**
//      * @read operations
//      */
//     async getItem(query: T) {
//         const flag = 'DDBCommand.getItem:'
//         try {
//             const inputCommand: AWS_DDB.GetItemCommandInput = {
//                 TableName: this.tableName,
//                 Key: marshall(query),
//                 ConsistentRead: true,
//             }

//             const item = (await dynamoClient.send(new AWS_DDB.GetItemCommand(inputCommand))).Item
//             if (item) return unmarshall(item) as T
//             else return;

//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);

//         }
//     }


//     async batchGetItemPK(keys: any[], options?: { projection?: string, exprAttributes?: any }): Promise<Record<string, any>[]> {
//         const flag = 'DDBCommand.batchGetItemPK:'
//         try {
//             if (!keys.length) return []
//             const inputCommand: AWS_DDB.BatchGetItemCommandInput = {
//                 RequestItems: {
//                     [this.tableName]: {
//                         Keys: keys,
//                         ConsistentRead: true,
//                         ProjectionExpression: options?.projection,
//                         ExpressionAttributeNames: options?.exprAttributes
//                     }
//                 }
//             }

//             const items = (await dynamoClient.send(new AWS_DDB.BatchGetItemCommand(inputCommand))).Responses?.[this.tableName]
//             if (items?.length) return items.map(e => unmarshall(e))
//             else return [];
//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }


//     async queryItems(conditionExpr: { key: string, value: string }, filterExpr?: string, filter?: T, options?: { limit?: number, lastEvalKey?: string, sort?: '1' | '-1' }) {
//         const flag = 'DDBCommand.queryItems:'
//         try {

//             let pk = `:_${conditionExpr.key}`
//             const expressionAttributeValues: any = {
//                 [pk]: conditionExpr.value
//             }
//             for (let key in filter) {
//                 expressionAttributeValues[`:_${key}`] = filter[key];
//             }

//             const inputCommand: AWS_DDB.QueryCommandInput = {
//                 TableName: this.tableName,
//                 Limit: Number(options?.limit),
//                 ConsistentRead: true,
//                 ExclusiveStartKey: options?.lastEvalKey ? { _id: { S: options?.lastEvalKey } } : undefined,
//                 KeyConditionExpression: `${conditionExpr.key} = ${pk}`,
//                 FilterExpression: filterExpr,
//                 ExpressionAttributeValues: marshall(expressionAttributeValues),
//                 ScanIndexForward: options?.sort === '1'
//             }

//             const items = (await dynamoClient.send(new AWS_DDB.QueryCommand(inputCommand))).Items
//             if (items?.length) return items.map(e => unmarshall(e)) as T[]
//             else return [];

//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }


//     async queryItemsPartitionSortKey(conditionExpr: { key: string, value: string }, filter?: T, options?: { sort?: '1' | '-1' }) {
//         const flag = 'DDBCommand.queryItems:'
//         try {

//             let pk = `:_${conditionExpr.key}`
//             const expressionAttributeValues: any = {
//                 [pk]: conditionExpr.value
//             }
//             for (let key in filter) {
//                 expressionAttributeValues[`:_${key}`] = filter[key];
//             }

//             const inputCommand: AWS_DDB.QueryCommandInput = {
//                 TableName: this.tableName,
//                 KeyConditionExpression: `${conditionExpr.key} = ${pk}`,
//                 ExpressionAttributeValues: marshall(expressionAttributeValues),
//                 ScanIndexForward: options?.sort === '1'
//             }

//             const items = (await dynamoClient.send(new AWS_DDB.QueryCommand(inputCommand))).Items
//             if (items?.length) return items.map(e => unmarshall(e)) as T[]
//             else return [];

//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }


//     async queryItemsGSI(
//         conditionExpr: KeyCondExpr,
//         filterExpr?: string,
//         filter?: T,
//         options?: { limit?: number, lastEvalKey?: string, sort?: '1' | '-1', indexName?: string }) {

//         const flag = 'DDBCommand.queryItemsGSI:'
//         try {

//             let key_cond_expr = ""
//             let pk = `:_${conditionExpr.pk}`
//             let sk = `:_${conditionExpr.sk}`
//             const expressionAttributeValues: any = {
//                 [pk]: conditionExpr.pv
//             }
//             key_cond_expr = `${conditionExpr.pk} = ${pk}`

//             if (conditionExpr.sk) {
//                 expressionAttributeValues[sk] = conditionExpr.sv
//                 key_cond_expr += ` AND ${conditionExpr.sk} = ${sk}`
//             }

//             for (let key in filter) {
//                 expressionAttributeValues[`:_${key}`] = filter[key];
//             }

//             const inputCommand: AWS_DDB.QueryCommandInput = {
//                 TableName: this.tableName,
//                 IndexName: options?.indexName,
//                 KeyConditionExpression: key_cond_expr,
//                 FilterExpression: filterExpr,
//                 ExpressionAttributeValues: marshall(expressionAttributeValues),
//                 ExclusiveStartKey: options?.lastEvalKey ? { _id: { S: options?.lastEvalKey } } : undefined,
//                 Limit: options?.limit ? Number(options?.limit) : undefined,
//                 ScanIndexForward: options?.sort ? options?.sort === '1' : undefined,
//             }
//             Util.removeUndefinedProps(inputCommand)

//             const { Items, LastEvaluatedKey } = (await dynamoClient.send(new AWS_DDB.QueryCommand(inputCommand)))
//             if (Items?.length) return { data: Items.map(e => unmarshall(e)) as T[], lastEvalKey: LastEvaluatedKey }
//             else return { data: [], lastEvalKey: "" };

//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }


//     async searchSongsGSI(
//         search_text: string,
//         options?: { limit?: number, exclusiveStartKey?: string }
//     ) {
//         const flag = "DDBCommand.searchSongsGSI:"
//         try {
//             const inputCommand: AWS_DDB.ScanCommandInput = {
//                 TableName: this.tableName,
//                 IndexName: 'normalized_title-song_status-index',
//                 // ConsistentRead:true,
//                 FilterExpression: "contains(normalized_title, :_normalized_title) AND song_status = :_song_status",
//                 ExpressionAttributeValues: marshall({
//                     ':_normalized_title': search_text,
//                     ':_song_status': 'active'
//                 }),
//                 ExclusiveStartKey: options?.exclusiveStartKey ? { _id: { S: options?.exclusiveStartKey } } : undefined,
//                 Limit: options?.limit ? Number(options?.limit) : undefined,
//                 ProjectionExpression: ProjectionKey.get_master_song,
//                 ExpressionAttributeNames: { "#id": "_id" }
//             };
//             Util.removeUndefinedProps(inputCommand)

//             const { Items, LastEvaluatedKey } = (await dynamoClient.send(new AWS_DDB.ScanCommand(inputCommand)))
//             if (Items?.length) return { data: Items.map(e => unmarshall(e)) as T[], lastEvalKey: LastEvaluatedKey }
//             else return { data: [], lastEvalKey: "" };
//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }


//     async scanItems(filterExpr: string, filter?: T, options?: { limit?: number, lastEvalKey?: string, sort?: '1' | '-1', projection?: string, exprAttributes?: any }) {
//         const flag = 'DDBCommand.scanItems:'
//         try {

//             const expressionAttributeValues: any = {}
//             for (let key in filter) {
//                 expressionAttributeValues[`:_${key}`] = filter[key];
//             }

//             const inputCommand: AWS_DDB.ScanCommandInput = {
//                 TableName: this.tableName,
//                 ConsistentRead: true,
//                 FilterExpression: filterExpr,
//                 ExpressionAttributeValues: marshall(expressionAttributeValues),
//                 Limit: options?.limit,
//                 ExclusiveStartKey: options?.lastEvalKey ? { _id: { S: options.lastEvalKey } } : undefined,
//                 ProjectionExpression: options?.projection,
//                 ExpressionAttributeNames: options?.exprAttributes
//                 // ScanIndexForward: options?.sort === '1'
//             }

//             const { Items, LastEvaluatedKey } = (await dynamoClient.send(new AWS_DDB.ScanCommand(inputCommand)))
//             if (Items?.length) return { data: Items.map(e => unmarshall(e)) as T[], lastEvalKey: LastEvaluatedKey }
//             else return { data: [], lastEvalKey: "" };

//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }

//     /**
//      * @write operations
//      */
//     async putItem(data: T) {
//         const flag = 'DDBCommand.putItem:'
//         try {
//             const inputCommand: AWS_DDB.PutItemCommandInput = {
//                 TableName: this.tableName,
//                 Item: marshall(data),

//                 // ReturnValues: 'ALL_OLD'
//             }
//             const upserted = (await dynamoClient.send(new AWS_DDB.PutItemCommand(inputCommand))).$metadata.httpStatusCode === HTTPCode.$200
//             if (upserted) return { data }
//             else return { data: undefined };

//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }

//     async updateItem(_id: string, val1: string, val2: string) {
//         const flag = 'DDBCommand.updateItem:'
//         try {
//             const inputCommand: AWS_DDB.UpdateItemCommandInput = {
//                 TableName: this.tableName,
//                 Key: {
//                     "_id": { S: _id }
//                 },
//                 UpdateExpression: `SET song_path = :_song_path, lyrics_timeline_file_path = :_lyrics_timeline_file_path`, // Update expression for multiple attributes
//                 ExpressionAttributeValues: {
//                     ":_song_path": { S: val1 }, // New value for AttributeName1
//                     ":_lyrics_timeline_file_path": { S: val2 } // New value for AttributeName2 (assuming it's a number)
//                 }
//             }

//             const update = (await dynamoClient.send(new AWS_DDB.UpdateItemCommand(inputCommand)))
//             return
//         } catch (error: any) {
//             console.error(flag, error.message)
//             throw new Error(error.message);
//         }
//     }

//     async updateItemByPK(key: T, data: T) {
//         const flag = 'DDBCommand.updateItemByPK:'
//         try {

//             let updateExpr = ""
//             let ExprAttributeValues: any = {}

//             for (let key in data) {
//                 updateExpr += `${key} = :_${key}`;
//                 ExprAttributeValues[`:_${key}`] = data[key]
//             }

//             const inputCommand: AWS_DDB.UpdateItemCommandInput = {
//                 TableName: this.tableName,
//                 Key: marshall(key),
//                 UpdateExpression: `SET ${updateExpr}`,
//                 ExpressionAttributeValues: marshall(ExprAttributeValues),
//                 ConditionExpression: 'attribute_exists(#id)',
//                 ExpressionAttributeNames: { "#id": "_id" }
//             };

//             await dynamoClient.send(new AWS_DDB.UpdateItemCommand(inputCommand))
//             return true

//         } catch (error: any) {
//             console.error(flag, error.message)
//             if (error.name === 'ConditionalCheckFailedException') {
//                 return false
//             }
//             throw new Error(error.message);
//         }
//     }

//     async updateItemByEmail(key: T, data: T) {
//         const flag = 'DDBCommand.updateItemByEmail:'
//         try {

//             let updateExpr = ""
//             let ExprAttributeValues: any = {}

//             for (let key in data) {
//                 updateExpr += `${key} = :_${key}`;
//                 ExprAttributeValues[`:_${key}`] = data[key]
//             }

//             const inputCommand: AWS_DDB.UpdateItemCommandInput = {
//                 TableName: this.tableName,
//                 Key: marshall(key),
//                 UpdateExpression: `SET ${updateExpr}`,
//                 ExpressionAttributeValues: marshall(ExprAttributeValues),
//                 // ConditionExpression: 'attribute_exists(#id)',
//                 // ExpressionAttributeNames: { "#id": "_id" }
//             };

//             await dynamoClient.send(new AWS_DDB.UpdateItemCommand(inputCommand))
//             return true

//         } catch (error: any) {
//             console.error(flag, error.message)
//             if (error.name === 'ConditionalCheckFailedException') {
//                 return false
//             }
//             throw new Error(error.message);
//         }
//     }

// }


// export default class DynamoDb {

//     static gen_id() {
//         return crypto.createHash('sha256').update(Date.now().toString()).digest('hex').substring(0, 28)
//     }

//     static SongMaster = new DynamoDBCommand<SongMasterBean>('song_master');
//     static Users = new DynamoDBCommand<UserBean>('users');
//     static UserRecordings = new DynamoDBCommand<UserRecordingBean>('user_recordings');
//     static AnnotatedSongs = new DynamoDBCommand<AnnotatedSongBean>('annotated_songs');
//     static UserPreferences = new DynamoDBCommand<UserPreferencesBean>('user_preferences');
//     static ArtistMaster = new DynamoDBCommand<ArtistMasterBean>('artist_master');
//     static CommercialGenreMaster = new DynamoDBCommand<CommercialGenreMasterBean>('commercial_genre_master');
//     static UserHomeFeedTiles = new DynamoDBCommand<HomeFeedTilesBean>('user_home_feed_tiles');
//     static UserHomeFeedGrids = new DynamoDBCommand<HomeFeedGridsBean>('user_home_feed_grid');
// }


import mongoose from 'mongoose'
import { Env } from '../Env'

export async function connectMongoDB() {
    try {
        console.log("mongodb connecting...");

        await mongoose.connect(Env.mongodb_url, {
            retryWrites: true,
        })
        console.log("mongodb connected");

    } catch (error: any) {
        console.error("mongodb:", error.message)
    }
}