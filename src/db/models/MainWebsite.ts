import { model, Schema, Types } from "mongoose"

export interface WebSignupFromBean {
    _id: Types.ObjectId
    first_name: string,
    // last_name: string,
    email: string,
    phone?: string,
    help_asked?: string
    find_us_via?: string
    shared_audio_url?: string
    social_media_link?: string
    uploaded_audio_path?: string,
    created_at: number

}

const webSignupFormSchema = new Schema<WebSignupFromBean>({
    email: {
        required: true,
        type: String,
        trim: true,
        unique: true
    },
    first_name: {
        required: true,
        type: String,
        trim: true,
    },
    phone: {
        required: false,
        type: String,
        trim: true,
    },
    help_asked: {
        required: false,
        type: String,
        trim: true,
    },
    find_us_via: {
        required: false,
        type: String,
        trim: true,
    },
    social_media_link: {
        required: false,
        type: String,
        trim: true,
    },
    shared_audio_url: {
        required: false,
        type: String,
        trim: true,
    },
    uploaded_audio_path: {
        required: false,
        type: String,
        trim: true,
    },
    created_at: {
        required: false,
        type: Number,
    }
}, { collection: "web_signup_forms" })

const WebSignupFromModel = model<WebSignupFromBean>("web_signup_forms", webSignupFormSchema)
export default WebSignupFromModel