import { Schema, model, Types } from "mongoose"


export interface ArtistMasterBean {
    _id: Types.ObjectId
    artist_name: string
    thumbnail_path: string
    is_active: boolean
}

const artistMasterSchema = new Schema<ArtistMasterBean>({
    artist_name: {
        required: true,
        trim: true,
        type: String
    },
    thumbnail_path: {
        required: true,
        trim: true,
        type: String
    },
    is_active: {
        required: true,
        type: Boolean
    }
}, { collection: "artist_master" })

artistMasterSchema.index(
    { is_active: 1, artist_name: 1 },
    { collation: { locale: "en", strength: 2 } }
)

const ArtistMasterModel = model<ArtistMasterBean>("artist_master", artistMasterSchema)
export default ArtistMasterModel
