import { Schema, model, Types } from "mongoose"
import { SongScale } from "./AnnotatedSongs"

export const RecordingListProjectionKey = {
    _id: 1,
    title: 1,
    genre: 1,
    genre_id: 1,
    scale: 1,
    tempo: 1,
    thumbnail_path: 1,
    master_song_id:1,
    is_deleted: 1,
    created_at: 1,
    is_final_save: 1,
    final_mixed_audio_path: 1,
    final_video_file_path: 1
} as const

export interface UserRecordingBean {
    _id: Types.ObjectId
    user_id?: Types.ObjectId
    master_song_id?: Types.ObjectId
    title?: string
    genre?: string
    genre_id?: string
    scale?: string
    tempo?: string
    vocal_filter_name?: string
    vocal_volume?: string
    bgm_volume?: string
    latency?: string
    denoise?: boolean
    raw_audio_file_path: string
    denoised_vocal_audio_path?: string
    filtered_vocal_audio_path?: string
    thumbnail_path?: string
    is_deleted?: boolean
    input_mic?: string
    device?: string
    os?: string
    created_at?: number
    is_final_save?: boolean
    
    master_filter_name?: string
    final_mixed_audio_path?: string
    /**@deprecated */
    final_video_file_path?: string
}



const userRecordingSchema = new Schema<UserRecordingBean>({
    user_id: {
        required: true,
        type: Schema.Types.ObjectId,
        ref: "users"
    },
    master_song_id: {
        required: true,
        type: Schema.Types.ObjectId,
        ref: "song_master"
    },
    title: {
        required: true,
        type: String,
        trim: true
    },
    genre: {
        required: true,
        type: String,
        trim: true
    },
    genre_id: {
        required: true,
        type: String,
        trim: true
    },
    scale: {
        required: true,
        type: String,
        enum: SongScale
    },
    tempo: {
        required: true,
        type: String,
        trim: true
    },
    vocal_filter_name: {
        required: false,
        type: String,
        trim: true
    },
    vocal_volume: {
        required: true,
        type: String,
        trim: true
    },
    bgm_volume: {
        required: true,
        type: String,
        trim: true
    },
    latency: {
        required: false,
        type: String,
        trim: true
    },
    raw_audio_file_path: {
        required: true,
        type: String,
        trim: true
    },
    
    final_mixed_audio_path: {
        required: false,
        type: String,
        trim: true
    },
    master_filter_name: {
        required: false,
        type: String,
        trim: true
    },
    denoised_vocal_audio_path: {
        required: false,
        type: String,
        trim: true
    },
    filtered_vocal_audio_path: {
        required: false,
        type: String,
        trim: true
    },
    input_mic: {
        required: false,
        type: String,
        trim: true
    },
    device: {
        required: false,
        type: String,
        trim: true
    },
    os: {
        required: false,
        type: String,
        trim: true
    },
    thumbnail_path: {
        required: true,
        type: String,
        trim: true
    },
    is_deleted: {
        required: true,
        type: Boolean,
    },
    denoise: {
        required: true,
        type: Boolean,
    },
    created_at: {
        required: true,
        type: Number
    },
    is_final_save: {
        required: false,
        type: Boolean,
    },
    final_video_file_path: {
        required: false,
        type: String,
        trim: true
    }
})

userRecordingSchema.index({ user_id: 1 })

const UserRecordingModel = model<UserRecordingBean>("user_recordings", userRecordingSchema)
export default UserRecordingModel