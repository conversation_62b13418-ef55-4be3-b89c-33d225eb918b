import { Schema, Types, model } from 'mongoose';

export interface PushNotiContent {
    title: string
    body: string
}
export enum ConfigType {
    USER_CONTENT_EMAILS = "user_content_emails",
    NOTIFIED_SONG_ID = "notified_song_ids",
    PUSH_NOTI = "push_noti"
}
export interface ConfigBean {
    _id: Types.ObjectId
    type: ConfigType
    user_content_emails: any
    notified_song_ids: Types.ObjectId[]
    push_noti: {
        sent_index: number
        contents: PushNotiContent[]
    }
}


const configModelSchema = new Schema<ConfigBean>({
    type: {
        required: true,
        type: String,
        enum: ConfigType
    },
    user_content_emails: {
        required: false,
        type: Schema.Types.Mixed
    },
    notified_song_ids: {
        required: false,
        type: [{ type: Schema.Types.ObjectId }]
    },
    push_noti: {
        required: false,
        type: Schema.Types.Mixed
    }
}, { collection: "configs" });

const ConfigModel = model<ConfigBean>("configs", configModelSchema)
export default ConfigModel;
