import { model, Schema, Types } from "mongoose";
import { SongLanguage } from "./SongMaster";

export enum CoverFeedMediaType {
    audio = "audio",
    video = "video",
}
export enum CoverFeedType {
    demo_cover = "demo_cover",
    artist_cover = "artist_cover",
    direct_upload = "direct_upload"
}

export interface CoverFeedBean {
    _id: Types.ObjectId;
    title: string;
    singer: string;
    melodyze_artist?: string;
    feed_type: CoverFeedType,
    thumbnail_path?: string
    master_song_id?: Types.ObjectId;
    annotated_song_id?: Types.ObjectId;
    genre: string
    genre_id: string
    scale: string;
    tempo: string;
    created_at: number;
    is_deleted: boolean;
    media_type: CoverFeedMediaType;
    final_mixed_audio_path?: string;
    final_video_file_path?: string;
    final_video_file_path_raw?: string; // raw path
    lang?: SongLanguage
}

const coverFeedSchema = new Schema<CoverFeedBean>({
    title: {
        required: true,
        type: String,
        trim: true
    },
    singer: {
        required: true,
        type: String,
        trim: true
    },
    melodyze_artist: {
        required: false,
        type: String,
        trim: true
    },
    thumbnail_path: {
        required: false,
        type: String,
        trim: true
    },
    master_song_id: {
        required: false,
        type: Schema.Types.ObjectId
    },
    annotated_song_id: {
        required: false,
        type: Schema.Types.ObjectId
    },
    genre: {
        required: true,
        type: String,
        trim: true
    },
    genre_id: {
        required: true,
        type: String,
        trim: true
    },
    scale: {
        required: true,
        type: String,
        trim: true
    },
    tempo: {
        required: true,
        type: String,
        trim: true
    },
    created_at: {
        required: true,
        type: Number,
    },
    is_deleted: {
        required: true,
        type: Boolean,
    },
    media_type: {
        required: true,
        type: String,
        enum: CoverFeedMediaType
    },
    feed_type: {
        required: true,
        type: String,
        enum: CoverFeedType
    },
    lang:{
        required: false,
        type: String,
        enum: SongLanguage
    },
    final_video_file_path: {
        required: false,
        type: String,
        trim: true
    },
    final_mixed_audio_path: {
        required: false,
        type: String,
        trim: true
    },
    final_video_file_path_raw: {
        required: false,
        type: String,
        trim: true
    },

})

// coverFeedSchema.index({ feed_type: 1, is_deleted: 1, genre: 1 })

const CoverFeedModel = model<CoverFeedBean>("user_feeds", coverFeedSchema)
export default CoverFeedModel;