import { Types, Schema, model } from "mongoose"


export interface HomeFeedTilesBean {
    _id: Types.ObjectId
    user_id: Types.ObjectId
    is_universal?: boolean
    tile_order: number
    tile_name: string
    song_id_list_map?: { [key: string]: string }
}


const homeFeedTilesSchema = new Schema<HomeFeedTilesBean>({
    user_id: {
        required: true,
        type: Schema.Types.ObjectId,
        ref: "users"
    },
    is_universal: {
        required: false,
        type: Boolean,
    },
    tile_order: {
        required: true,
        type: Number,
    },
    tile_name: {
        required: true,
        type: String,
        trim: true
    },
    song_id_list_map: {
        required: false,
        type: Object,
    }
})

const HomeFeedTilesModel = model<HomeFeedTilesBean>("user_home_feed_tiles", homeFeedTilesSchema)
export default HomeFeedTilesModel