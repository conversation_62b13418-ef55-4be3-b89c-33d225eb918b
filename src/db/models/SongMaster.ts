import { Schema, model, ObjectId, Types } from "mongoose"

export const SongMasterProjectionKey = {
    _id: 1,
    title: 1,
    singer: 1,
    lang: 1,
    default_genre: 1,
    default_genre_id: 1,
    time_signature: 1,
    thumbnail_path: 1,
} as const
export interface SongMasterBean {
    _id: Types.ObjectId
    title: string
    normalized_title?: string
    created_at: number
    song_status: string
    thumbnail_path: string
    default_genre?: string
    default_genre_id?: string
    time_signature?: string
    singer?: string
    lang?: SongLanguage
    pending_songs_count?: number
    is_pending_songs?: boolean
}

export enum SongStatus {
    active = "active",
    verified = "verified",
    not_verified = "not_verified"
}

export enum DawName {
    cubase = "Cubase",
    logic = "Logic"
}

export enum SongLanguage {
    ENGLISH = "english",
    HINDI = "hindi",
    BENGALI = "bengali",
    
}

const songMasterSchema = new Schema<SongMasterBean>({
    title: {
        required: true,
        trim: true,
        type: String
    },
    normalized_title: {
        required: true,
        trim: true,
        type: String
    },
    created_at: {
        required: true,
        type: Number
    },
    song_status: {
        required: true,
        type: String,
        enum: SongStatus
    },
    thumbnail_path: {
        required: true,
        trim: true,
        type: String
    },
    default_genre: {
        required: true,
        trim: true,
        type: String
    },
    default_genre_id: {
        required: true,
        trim: true,
        type: String
    },
    time_signature: {
        required: true,
        type: String,
        // enum: ["4_by_4"]
    },
    singer: {
        required: true,
        trim: true,
        type: String
    },
    lang: {
        required: true,
        type: String,
        enum: SongLanguage
    },
    pending_songs_count: {
        required: false,
        type: Number
    },
    is_pending_songs: {
        required: true,
        type: Boolean
    },
}, { collection: "song_master" })


songMasterSchema.index(
    { song_status: 1, normalized_title: 1 }
)

const SongMasterModel = model<SongMasterBean>("song_master", songMasterSchema)
export default SongMasterModel