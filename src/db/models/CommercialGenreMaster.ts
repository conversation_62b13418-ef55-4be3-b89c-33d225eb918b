import { Schema, model, Types } from "mongoose"


export interface CommercialGenreMasterBean {
    _id?: Types.ObjectId
    genre_name?: string
    thumbnail_path?: string
    is_active?: boolean
}

const commercialGenreMasterSchema = new Schema<CommercialGenreMasterBean>({
    genre_name: {
        required: true,
        trim: true,
        type: String
    },
    thumbnail_path: {
        required: true,
        trim: true,
        type: String
    },
    is_active: {
        required: true,
        type: Boolean
    }
}, { collection: "commercial_genre_master" })

const CommercialGenreMasterModel = model<CommercialGenreMasterBean>("commercial_genre_master", commercialGenreMasterSchema)
export default CommercialGenreMasterModel