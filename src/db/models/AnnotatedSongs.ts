import { model, Schema, Types } from "mongoose"
import { DawName, SongStatus } from "./SongMaster"

export interface AnnotatedSongBean {
    _id: Types.ObjectId
    master_song_id: Types.ObjectId
    song_path?: string
    wav_song_path?: string
    genre: string
    genre_id: string
    scale: string
    tempo: string
    is_default?: boolean
    song_status: string
    created_at: number
    daw_name?: string
    lyrics_timeline_file_path?: string
    project_file_path?: string
    midi_file_path?: string
    guide_vocal_path?: string
    // modified_at?: number
    // created_by?: string
    // modified_by?: string
    // annotator_id?: string
    // test?: true
}

export const AnnotatedSongsProjectKeysUI = {
    _id: 1,
    master_song_id: 1,
    song_path: 1,
    guide_vocal_path: 1,
    genre: 1,
    genre_id: 1,
    scale: 1,
    tempo: 1,
    is_default: 1,
    lyrics_timeline_file_path: 1,
} as const

export enum SongScale {
    A = "A",
    A_sharp = "A_sharp",
    B = "B",
    C = "C",
    C_sharp = "C_sharp",
    D = "D",
    D_sharp = "D_sharp",
    E = "E",
    F = "F",
    F_sharp = "F_sharp",
    G = "G",
    G_sharp = "G_sharp"
}

const annotatedSongSchema = new Schema<AnnotatedSongBean>({
    master_song_id: {
        required: true,
        type: Schema.Types.ObjectId,
        ref: "song_master"
    },
    song_path: {
        required: true,
        type: String,
        trim: true
    },
    wav_song_path: {
        required: false,
        type: String,
        trim: true
    },
    genre: {
        required: true,
        type: String,
        trim: true
    },
    genre_id: {
        required: true,
        type: String,
        trim: true
    },
    scale: {
        required: true,
        type: String,
        enum: SongScale
    },
    tempo: {
        required: true,
        type: String,
        trim: true
    },
    is_default: {
        required: false,
        type: Boolean,
    },
    song_status: {
        required: true,
        type: String,
        enum: SongStatus
    },
    created_at: {
        required: true,
        type: Number,
    },
    daw_name: {
        required: false,
        type: String,
        enum: DawName
    },
    lyrics_timeline_file_path: {
        required: true,
        type: String,
        trim: true
    },
    guide_vocal_path: {
        required: false,
        type: String,
        trim: true
    },
    project_file_path: {
        required: true,
        type: String,
        trim: true
    },
    midi_file_path: {
        required: true,
        type: String,
        trim: true
    }
})

const AnnotatedSongModel = model<AnnotatedSongBean>("annotated_songs", annotatedSongSchema)
export default AnnotatedSongModel