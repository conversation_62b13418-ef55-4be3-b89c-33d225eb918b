import { Schema, model, Types } from "mongoose"

export enum PNDeviceType {
    ios = "ios",
    android = "android",
    web = "web"
}

export interface PNDeviceInfo {
    fcm_token: string
    device_id: string
    device_type: PNDeviceType
    updated_at: number 
}
export interface UserBean {
    _id: Types.ObjectId
    email_id: string
    user_name: string
    created_at: number
    is_deleted: boolean
    updated_at?: number
    profile_picture_path?: string
    app_theme?: any
    pn_device_list?: PNDeviceInfo[]
}

const pnDeviceSchema = new Schema<PNDeviceInfo>({
    device_id: {
        required: true,
        trim: true,
        type: String
    },
    fcm_token: {
        required: true,
        trim: true,
        type: String
    },
    device_type: {
        required: false,
        type: String,
        enum: PNDeviceType
    },
    updated_at: {
        required: true,
        type: Number
    }
})


const userSchema = new Schema<UserBean>({
    email_id: {
        required: true,
        trim: true,
        type: String,
        lowercase: true
    },
    user_name: {
        required: true,
        trim: true,
        type: String
    },
    created_at: {
        required: true,
        type: Number
    },
    is_deleted: {
        required: true,
        type: Boolean
    },
    updated_at: {
        required: false,
        type: Number
    },
    app_theme: {
        required: false,
        type: Schema.Types.Mixed
    },
    profile_picture_path: {
        required: false,
        trim: true,
        type: String
    },
    pn_device_list: {
        _id: false,
        required: false,
        type: [pnDeviceSchema]
    }

}, { collection: "users" })

// userSchema.index({ email_id: 1 }, { unique: true, partialFilterExpression: { is_deleted: false } });
const UserModel = model<UserBean>("users", userSchema)



export default UserModel