import { Schema, model, Types } from "mongoose"


export interface UserPreferencesBean {
    _id: Types.ObjectId
    user_id: Types.ObjectId
    commercial_genre_ids?: string[]
    artist_ids?: string[]
}

const userPreferencesSchema = new Schema<UserPreferencesBean>({
    user_id: {
        required: true,
        type: Schema.Types.ObjectId,
        ref: "users",
        unique: true
    },
    commercial_genre_ids: [{
        required: true,
        type: String,
        ref: "commercial_genre_master"
    }],
    artist_ids: [{
        required: true,
        type: String,
        ref: "artist_master"
    }]
})

const UserPreferencesModel = model<UserPreferencesBean>("user_preferences", userPreferencesSchema)
export default UserPreferencesModel