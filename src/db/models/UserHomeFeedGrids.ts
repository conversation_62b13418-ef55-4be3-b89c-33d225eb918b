import { Schema, model, Types } from "mongoose"


enum GridType {
    artist = "artist",
    genre = "genre"
}

export interface HomeFeedGridsBean {
    _id: Types.ObjectId
    user_id: Types.ObjectId
    is_universal?: boolean
    grid_order?: number
    grid_type?: GridType
    genre_id?: Types.ObjectId
    artist_id?: Types.ObjectId
    song_id_list_map?: { [key: string]: string }
}


const homeFeedGridSchema = new Schema<HomeFeedGridsBean>({
    user_id: {
        required: true,
        type: Schema.Types.ObjectId,
        ref: "users"
    },
    is_universal: {
        required: false,
        type: Boolean,
    },
    grid_order: {
        required: true,
        type: Number,
    },
    grid_type: {
        required: true,
        type: String,
        enum: GridType
    },
    genre_id: {
        required: false,
        type: Types.ObjectId,
        ref: "commercial_genre_master"
    },
    artist_id: {
        required: false,
        type: Types.ObjectId,
        ref: "artist_master"
    },
    song_id_list_map: {
        required: true,
        type: Object,  // Use a Map to allow dynamic keys

    }
})

const HomeFeedGridsModel = model<HomeFeedGridsBean>("user_home_feed_grids", homeFeedGridSchema)
export default HomeFeedGridsModel

