import { Types } from "mongoose";
import Memory<PERSON><PERSON><PERSON><PERSON>leton from "../utils/MemoryCache";
import GenericDataService, { IGenericDataOptions } from "./generic-data.service";
import ArtistMasterModel, { ArtistMasterBean } from "../db/models/ArtistMaster";
import awsS3 from "../utils/StorageHelper";
import Utils from "../utils/Utils";


class ArtistMasterDataService extends GenericDataService<ArtistMasterBean> {

    constructor() {
        super(ArtistMasterModel, MemoryCacheSingleton);
    }

    private signedUrlExp = new Map<string, number>();

    async getArtists(options: IGenericDataOptions<ArtistMasterBean>) {
        return (await this.find(options)).map(Utils.deepCloneWithObjectId)
    }

    async getArtistByIds(artistIds: Types.ObjectId[]) {
        const artists = await this.getByUids(artistIds, { filter: { is_active: true } });
        const cacheTTLms = this.getCacheTTLms(24);
        const s3TTL = this.getS3TTLms(cacheTTLms);

        // TODO: Need to common function to stop repeatation
        await Promise.all(
            artists.map(async (artist) => {
                const expKey = `${ArtistMasterModel.modelName}:${artist._id.toString()}`;
                let isUrlExpired = false;

                if (this.signedUrlExp.has(expKey) && !artist.thumbnail_path.startsWith("/")) {
                    const exp = this.signedUrlExp.get(expKey);
                    if (exp && exp <= Date.now()) {
                        isUrlExpired = true;
                        this.signedUrlExp.delete(expKey);
                    }
                } else {
                    isUrlExpired = true;
                }

                if (isUrlExpired) {
                    this.signedUrlExp.set(expKey, Date.now() + cacheTTLms);
                    if (artist.thumbnail_path) {
                        artist.thumbnail_path = await awsS3.getSignedUrl(artist.thumbnail_path, s3TTL);
                    }
                }
            })
        );

        return artists.map(Utils.deepCloneWithObjectId);
    }

}


const artistMasterDataService = new ArtistMasterDataService();
export default artistMasterDataService;
