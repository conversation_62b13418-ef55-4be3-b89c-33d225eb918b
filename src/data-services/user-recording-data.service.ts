import MemoryCacheSingleton from "../utils/MemoryCache";
import GenericDataService, { IGenericDataOptions } from "./generic-data.service";
import UserRecordingModel, { UserRecordingBean } from "../db/models/UserRecordings";
import Utils from "../utils/Utils";


class UserRecordingsDataService extends GenericDataService<UserRecordingBean> {
    constructor() {
        super(UserRecordingModel, MemoryCacheSingleton);
    }

    async getRecordings(options: IGenericDataOptions<UserRecordingBean>) {
        return (await this.find(options)).map(Utils.deepCloneWithObjectId)
    }
}

const userRecordingdDataService = new UserRecordingsDataService();
export default userRecordingdDataService;