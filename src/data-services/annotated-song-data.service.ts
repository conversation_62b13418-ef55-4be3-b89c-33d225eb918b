import MemoryCache<PERSON>ingleton from "../utils/MemoryCache";
import GenericDataService from "./generic-data.service";
import AnnotatedSongModel, { AnnotatedSongBean, AnnotatedSongsProjectKeysUI } from "../db/models/AnnotatedSongs";
import { SongStatus } from "../db/models/SongMaster";
import { Types } from "mongoose";
import awsS3 from "../utils/StorageHelper";


class AnnotatedSongDataService extends GenericDataService<AnnotatedSongBean> {
    constructor() {
        super(AnnotatedSongModel, MemoryCacheSingleton);
    }

    private signedUrlExp = new Map<string, number>();

    async getAllAnnotatedSongsById(id: string) {
        // TODO: Fix if memory cache deleted or expired before signedUrlExp & signedUrlExp not updated then signed Url getting malformed
        const cacheTTLms = this.getCacheTTLms(24)
        let isUrlExpired = false;
        const expKey = `${this.getAllAnnotatedSongsById.name}:${id}`;

        if (this.signedUrlExp.has(expKey)) {
            const exp = this.signedUrlExp.get(expKey);
            if (exp && exp <= Date.now()) {
                isUrlExpired = true;
                this.signedUrlExp.delete(expKey);
            }
        } else {
            isUrlExpired = true
        }

        const songs = await this.find({
            filter: { master_song_id: new Types.ObjectId(id), song_status: SongStatus.active },
            projection: AnnotatedSongsProjectKeysUI,
            cacheOptions: {
                forceRefresh: isUrlExpired,
                ttl: cacheTTLms
            }
        })

        if (isUrlExpired || songs.some(song => song?.song_path?.startsWith("/"))) {
            const expDate = Date.now() + cacheTTLms;
            this.signedUrlExp.set(expKey, expDate)
            await Promise.all(songs.map(async item => {
                const s3TTL = this.getS3TTLms(cacheTTLms)
                if (item.song_path) {
                    item.song_path = await awsS3.getSignedUrl(item.song_path, s3TTL);
                }
                if (item.lyrics_timeline_file_path) {
                    item.lyrics_timeline_file_path = await awsS3.getSignedUrl(item.lyrics_timeline_file_path, s3TTL);
                }
                if (item.guide_vocal_path) {
                    item.guide_vocal_path = await awsS3.getSignedUrl(item.guide_vocal_path, s3TTL);
                }
            }));
        }

        return songs;

    }
}

const annotatedSongDataService = new AnnotatedSongDataService();
export default annotatedSongDataService;