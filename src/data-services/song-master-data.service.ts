import { Types } from "mongoose";
import SongMasterModel, { SongMasterBean, SongMasterProjectionKey, SongStatus } from "../db/models/SongMaster";
import MemoryCacheSingleton from "../utils/MemoryCache";
import GenericDataService, { IGenericDataOptions } from "./generic-data.service";
import Utils from "../utils/Utils";
import awsS3 from "../utils/StorageHelper";


class SongMasterDataService extends GenericDataService<SongMasterBean> {

    constructor() {
        super(SongMasterModel, MemoryCacheSingleton);
    }

    private signedUrlExp = new Map<string, number>();

    async getActiveSongsByIds(ids: Types.ObjectId[]) {
        const songs = await this.getByUids(ids, {
            filter: { song_status: SongStatus.active },
            projection: SongMasterProjectionKey,
        })
        const cacheTTLms = this.getCacheTTLms(24);
        const s3TTL = this.getS3TTLms(cacheTTLms);

        // TODO: Need to common function to stop repeatation
        await Promise.all(
            songs.map(async (song) => {
                const expKey = `${SongMasterModel.modelName}:${song._id.toString()}`;
                let isUrlExpired = false;

                if (this.signedUrlExp.has(expKey) && !song.thumbnail_path.startsWith("/")) {
                    const exp = this.signedUrlExp.get(expKey);
                    if (exp && exp <= Date.now()) {
                        isUrlExpired = true;
                        this.signedUrlExp.delete(expKey);
                    }
                } else {
                    isUrlExpired = true;
                }

                if (isUrlExpired) {
                    this.signedUrlExp.set(expKey, Date.now() + cacheTTLms);
                    if (song.thumbnail_path) {
                        song.thumbnail_path = await awsS3.getSignedUrl(song.thumbnail_path, s3TTL);
                    }
                }
            })
        );
        return songs.map(Utils.deepCloneWithObjectId);
    }

    async getActiveSongById(id: Types.ObjectId) {
        const cacheTTLms = this.getCacheTTLms(24);
        const s3TTL = this.getS3TTLms(cacheTTLms);
        const expKey = `${SongMasterModel.modelName}:${id.toString()}`;
        let isUrlExpired = false;


        if (this.signedUrlExp.has(expKey)) {
            const exp = this.signedUrlExp.get(expKey);
            if (exp && exp <= Date.now()) {
                isUrlExpired = true;
                this.signedUrlExp.delete(expKey);
            }
        } else {
            isUrlExpired = true;
        }

        const song = await this.findOne({
            filter: { _id: id, song_status: SongStatus.active },
            projection: SongMasterProjectionKey,
            cacheOptions: {
                ttl: cacheTTLms,
                forceRefresh: isUrlExpired
            }
        })
        if (!song) return null;

        if (isUrlExpired || song.thumbnail_path.startsWith("/")) {
            this.signedUrlExp.set(expKey, Date.now() + cacheTTLms);
            if (song.thumbnail_path) {
                song.thumbnail_path = await awsS3.getSignedUrl(song.thumbnail_path, s3TTL);
            }
        }
        return Utils.deepCloneWithObjectId(song);
    }

    async getSongs(options: IGenericDataOptions<SongMasterBean>) {
        const songs = await this.find(options)
        return songs.map(Utils.deepCloneWithObjectId);
    }
}

const songMasterDataService = new SongMasterDataService();
export default songMasterDataService;