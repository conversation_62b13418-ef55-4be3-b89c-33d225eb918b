import { Types } from "mongoose";
import HomeFeedGridsModel, { HomeFeedGridsBean } from "../db/models/UserHomeFeedGrids";
import MemoryCacheSingleton from "../utils/MemoryCache";
import GenericDataService, { IGenericDataOptions } from "./generic-data.service";
import { Env } from "../Env";


class HomeGridsDataService extends GenericDataService<HomeFeedGridsBean> {

    constructor() {
        super(HomeFeedGridsModel, MemoryCacheSingleton);
    }

    async getGridsByUserId(artistIds: Types.ObjectId[], /**genreIds: Types.ObjectId[]*/) {
        return this.find({
            filter: {
                user_id: new Types.ObjectId(Env.RECSYS_UNIVERSAL_USER_ID),
                artist_id: { $in: artistIds },
                // genre_id: { $in: genreIds }
            },
            sort: { grid_order: 1 }
        })
    }
}

const homeGridsDataService = new HomeGridsDataService();
export default homeGridsDataService;
