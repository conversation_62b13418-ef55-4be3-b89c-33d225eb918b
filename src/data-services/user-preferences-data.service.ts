import { Types } from "mongoose";
import Memory<PERSON>ache<PERSON>ingleton from "../utils/MemoryCache";
import GenericDataService from "./generic-data.service";
import { UserPreferencesBean } from "../db/models/UserPreferences";
import UserPreferencesModel from "../db/models/UserPreferences";

class UserPreferencesDataService extends GenericDataService<UserPreferencesBean> {
    constructor() {
        super(UserPreferencesModel, MemoryCacheSingleton);
    }

    async getPreferences(userId: Types.ObjectId) {
        return await this.findOne({
            filter: { user_id: userId },
        })
    }
}

const userPreferencesDataService = new UserPreferencesDataService();
export default userPreferencesDataService;