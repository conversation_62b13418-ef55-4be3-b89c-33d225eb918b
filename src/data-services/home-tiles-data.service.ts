import HomeFeedTilesModel, { HomeFeedTilesBean } from "../db/models/UserHomeFeedTiles";
import MemoryCacheSingleton from "../utils/MemoryCache";
import GenericDataService, { IGenericDataOptions } from "./generic-data.service";


class HomeTilesDataService extends GenericDataService<HomeFeedTilesBean> {
    constructor() {
        super(HomeFeedTilesModel, MemoryCacheSingleton);
    }

    async getTiles(options: IGenericDataOptions<HomeFeedTilesBean>){
        return this.find(options)
    }

}

const homeTilesDataService = new HomeTilesDataService();
export default homeTilesDataService;
