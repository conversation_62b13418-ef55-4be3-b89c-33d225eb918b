import CoverFeedModel, { CoverFeedBean } from "../db/models/CoverFeed";
import MemoryCacheSingleton from "../utils/MemoryCache";
import awsS3 from "../utils/StorageHelper";
import Utils from "../utils/Utils";
import GenericDataService from "./generic-data.service";

class CoverFeedDataService extends GenericDataService<CoverFeedBean> {
    constructor() {
        super(CoverFeedModel, MemoryCacheSingleton);
    }
    private signedUrlExp = new Map<string, number>();

    async getAllCoverFeeds() {
        const cacheTTLms = this.getCacheTTLms(24);
        let isUrlExpired = false;
        const expKey = `${this.getAllCoverFeeds.name}`;
        if (this.signedUrlExp.has(expKey)) {
            const exp = this.signedUrlExp.get(expKey);
            if (exp && exp <= Date.now()) {
                isUrlExpired = true;
                this.signedUrlExp.delete(expKey);
            }
        } else {
            isUrlExpired = true
        }

        let feeds = await this.find({
            filter: { is_deleted: false },
            limit: 300,
            cacheOptions: {
                forceRefresh: isUrlExpired,
                ttl: cacheTTLms
            }
        })
        feeds = Utils.shuffleArray(feeds);

        if (isUrlExpired || feeds.some(feed => feed?.thumbnail_path?.startsWith("/"))) {
            const expDate = Date.now() + cacheTTLms;
            this.signedUrlExp.set(expKey, expDate)
            await Promise.all(feeds.map(async item => {
                const s3TTL = this.getS3TTLms(cacheTTLms);
                if (item.final_video_file_path) {
                    item.final_video_file_path = await awsS3.getSignedUrl(item.final_video_file_path, s3TTL);
                }
                if (item.final_mixed_audio_path) {
                    item.final_mixed_audio_path = await awsS3.getSignedUrl(item.final_mixed_audio_path, s3TTL);
                }
                if (item.thumbnail_path) {
                    item.thumbnail_path = await awsS3.getSignedUrl(item.thumbnail_path, s3TTL);
                }
            }));
        }

        return feeds;
    }
}

const coverFeedDataService = new CoverFeedDataService();
export default coverFeedDataService