import { Model, ProjectionType, RootFilterQuery, Types } from "mongoose";
import { IMemoryCache } from "../utils/MemoryCache";


export interface IGenericDataOptions<T> {
    filter: RootFilterQuery<T>;
    sort?: Record<string, 1 | -1>;
    projection?: ProjectionType<T>;
    limit?: number;
    skip?: number;
    cacheOptions?: IGenericDataCacheOptions;
}

export interface IGenericDataCacheOptions {
    ttl?: number;
    forceRefresh?: boolean;
    enableCache?: boolean;
    skipCaching?: boolean;
    invalidateCache?: boolean;
    customCacheKey?: string;
}

class GenericDataService<T extends { _id: Types.ObjectId }> {

    constructor(
        private readonly model: Model<T>,
        private readonly mCache: IMemoryCache
    ) { }

    protected generateCacheKey(id?: string, options?: IGenericDataOptions<T>): string {
        if (options?.cacheOptions?.customCacheKey) {
            return `${this.model.modelName}:${options.cacheOptions.customCacheKey}`;
        }

        const keyParts = [this.model.modelName];
        id && keyParts.push(`id=${id}`);

        if (options) {
            options.filter && keyParts.push(`ft:${JSON.stringify(options.filter)}`);
            options.sort && keyParts.push(`sr:${JSON.stringify(options.sort)}`);
            options.projection && keyParts.push(`pj:${JSON.stringify(options.projection)}`);
            options.limit && keyParts.push(`lm:${options.limit}`);
            options.skip && keyParts.push(`sk:${options.skip}`);
        }
        return keyParts.join(':');
    }

    protected getCacheTTLms(hours: number) {
        return hours * 60 * 60 * 1000;
    }

    protected getS3TTLms(cacheTTLms: number) {
        return (cacheTTLms / 1000) + 3600
    }

    async findById(id: Types.ObjectId, ttl?: number): Promise<T | null> {
        const cacheKey = this.generateCacheKey(id.toString());
        const cached = this.mCache.get<T>(cacheKey);

        if (cached) return cached;
        const data = await this.model.findById(id);

        if (data) {
            this.mCache.set(cacheKey, data, { ttl });
        }
        return data;
    }

    async findOne(options: IGenericDataOptions<T>): Promise<T | null> {
        const cacheKey = this.generateCacheKey(undefined, options);
        if (!options.cacheOptions?.forceRefresh) {
            const cached = this.mCache.get<T>(cacheKey);
            if (cached) return cached;
        }
        const data = await this.model.findOne(options.filter, options.projection).lean().exec();

        if (data && !options.cacheOptions?.skipCaching) {
            this.mCache.set(cacheKey, data, { ttl: options.cacheOptions?.ttl });
        }
        return data as T | null;
    }

    async find(options: IGenericDataOptions<T>): Promise<T[]> {
        // TODO: handle invalidateCache
        const cacheKey = this.generateCacheKey(undefined, options);
        if (!options.cacheOptions?.forceRefresh) {
            const cached = this.mCache.get<T[]>(cacheKey);
            if (cached) return cached;
        }
        const data = await this.model.find(
            options.filter,
            options.projection,
            {
                skip: options.skip,
                limit: options.limit,
                sort: options.sort
            }
        ).lean().exec();

        if (data && !options.cacheOptions?.skipCaching) {
            this.mCache.set(cacheKey, data, { ttl: options.cacheOptions?.ttl });
        }
        // return Utils.deepCloneWithObjectId(data) as T[];
        return data as T[];
    }

    protected async getByUids(ids: Types.ObjectId[], options: IGenericDataOptions<T>): Promise<T[]> {
        const cachedItems: T[] = [];
        const missingIds: Types.ObjectId[] = [];

        for (const id of ids) {
            const cached = this.mCache.get(`${this.model.modelName}:${id.toString()}`) as T | undefined;
            if (cached) {
                cachedItems.push(cached);
            } else {
                missingIds.push(id);
            }
        }

        let fetchedItems: any[] = []
        if (missingIds.length > 0) {
            fetchedItems = await this.model.find(
                { _id: { $in: missingIds }, ...options.filter },
                options.projection,
                {
                    skip: options.skip,
                    limit: options.limit,
                    sort: options.sort
                }
            ).lean().exec();

            for (const item of fetchedItems) {
                this.mCache.set(`${this.model.modelName}:${item._id.toString()}`, item);
            }
        }

        const allItems: T[] = ids.map(id =>
            cachedItems.find(item => item._id.equals(id)) ||
            fetchedItems.find(item => item._id.equals(id))
        ).filter(Boolean);

        return allItems
    }
}

export default GenericDataService;