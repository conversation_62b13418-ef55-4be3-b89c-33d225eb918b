{"name": "<PERSON><PERSON>", "version": "1.0.59", "description": "", "main": "index.js", "scripts": {"start:dev": "ts-node-dev --respawn --transpile-only ./src/index.ts", "start": "node dist/index.js", "build": "tsc -p .", "dev": "tsc -p . && node dist/index.js", "TEST:deploy": "sh test.deploy.sh"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@animesh-melodyze/melo-ts-modules": "^1.0.0", "@aws-sdk/client-s3": "^3.445.0", "@aws-sdk/client-sesv2": "^3.582.0", "@aws-sdk/s3-request-presigner": "^3.670.0", "axios": "^1.6.0", "bcrypt": "^5.1.1", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^12.1.0", "google-auth-library": "^9.2.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "mongoose": "^8.5.2", "multer": "^1.4.5-lts.1", "pm2": "^5.3.0"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.15", "@types/lodash": "^4.17.17", "@types/multer": "^1.4.11", "ts-node-dev": "^2.0.0", "typescript": "^4.9.5"}}